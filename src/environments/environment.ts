import { MandinerEnvironment } from './environment.definitions';

// Lok<PERSON>lis fejlesztői környezet
export const environment: MandinerEnvironment = {
  production: false,
  type: 'local',
  apiUrl: '/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  secureApiUrl: 'https://kozponti-api.dev.trendency.hu/secureapi/hu',
  financialApiUrl: 'https://findata.apptest.content.private/restapi',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '****************',
  googleClientId: '************-bd1qq5tklrrmvibl303b4f0q0ud1vmu2.apps.googleusercontent.com',
  appleClientId: 'service.hu.mandiner.sso',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-TC4WGP7',
  gemiusId: '..iQcfhzv7BVlRP0QFMNQJPZfStuU68PkYZybtIshY7.f7',
  httpReqTimeout: 30, // second
  shopUrls: {
    manageCards: 'https://shop.mandiner.hu/account',
    cancelSubscription: 'https://shop.mandiner.hu/account',
    subscriptions: 'https://www.mandiner-elofizetes.com/elofizetes-start',
  },
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
