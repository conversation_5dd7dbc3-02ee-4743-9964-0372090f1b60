import { Injectable } from '@angular/core';

import { DossierArticleShort, InitResolverData, InitResponse, LiveSport, SimplifiedMenuTree, TrendingTag } from '@trendency/kesma-ui';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ApiService, PortalConfigService, EbService, SportResultService } from '../services';
import { DossierCard } from '../definitions';

export const EB_SPORT_TITLE = 'Labdarúgás';
export const EB_COMPETITION_TITLE = 'EB';
@Injectable({ providedIn: 'root' })
export class InitResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly portalConfigService: PortalConfigService,
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService
  ) {}

  resolve(): Observable<InitResolverData> {
    return forkJoin([
      this.apiService.init().pipe(
        map(({ data }) => {
          this.portalConfigService.setConfig(data.portalConfigs);
          return data;
        }),
        switchMap((init: InitResponse) => {
          if (!this.ebService.isEnableFootballEbElements()) {
            return of(init);
          }
          return this.sportResultService.getLiveSports().pipe(
            map(({ data }) => data.find(({ title }) => title === EB_SPORT_TITLE) as LiveSport),
            switchMap(({ id }) => {
              return this.sportResultService.getCompetitions(id).pipe(
                map((comp) => comp?.data?.find((compData) => compData?.publicTitle === EB_COMPETITION_TITLE)),
                map((eb_comp) => {
                  this.ebService.setSlug(eb_comp?.competition?.slug as string);
                  return init;
                })
              );
            })
          );
        }),
        catchError(() => {
          return of({} as InitResponse);
        })
      ) as Observable<InitResponse>,
      this.apiService.getMenu().pipe(
        catchError(() => {
          return of({} as SimplifiedMenuTree);
        })
      ) as Observable<SimplifiedMenuTree>,
      this.apiService.getDossiers().pipe(
        map((res) => {
          return of(
            res.data.map((data: DossierCard) => {
              return {
                title: data.title,
                slug: data.slug,
                headerImage: data.thumbnail,
                mainArticle: {} as DossierArticleShort,
                secondaryArticles: [],
              };
            })
          );
        }),
        catchError(() => {
          return of([] as DossierCard[]);
        })
      ) as unknown as Observable<DossierCard[]>,
      this.apiService.getTagsOnHeaderBar().pipe(
        map((res) => {
          return of(
            res.data.map((data: TrendingTag) => {
              return {
                ...data,
                thumbnail: data.thumbnailUrl,
              };
            })
          );
        }),
        catchError(() => {
          return of([] as TrendingTag[]);
        })
      ) as Observable<TrendingTag[]>,
    ]).pipe(
      map<[InitResponse, SimplifiedMenuTree, DossierCard[], TrendingTag[]], InitResolverData>(([init, menu, dossiers, tags]) => ({
        init,
        menu,
        dossiers,
        tags,
      }))
    );
  }
}
