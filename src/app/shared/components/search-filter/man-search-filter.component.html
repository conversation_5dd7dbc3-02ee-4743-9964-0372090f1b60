<div *ngIf="formGroup" class="search">
  <div *ngIf="showSearchHeader" class="search-header">
    <h1 class="man-article-title">{{ articleTitle }}</h1>
    <div *ngIf="showedSearchedKeyWord" class="search-result-count">
      <span class="search-result-count-number">{{ resultCount || 0 }}</span>
      <span class="search-result-count-txt"> tal<PERSON>lat a következőre </span>
      <span class="search-result-count-match">{{ showedSearchedKeyWord }}</span>
    </div>
    <div *ngIf="!showedSearchedKeyWord || showedSearchedKeyWord.length === 0" class="search-result-count">
      <span class="search-result-count-txt"><PERSON><PERSON><PERSON> be a keresni kívánt szöveget</span>
    </div>
  </div>
  <div
    [ngClass]="{ 'without-search': !showSearchBar, 'filter-on-side': filterOnSide, active: active }"
    [style.margin-bottom.px]="getFilterWrapperHeight()"
    class="search-bar"
  >
    <div class="search-bar-append">
      <div *ngIf="showSearchBar" class="search-bar-wrap">
        <img alt="Nagyító" class="search-bar-icon" src="assets/images/icons/zoom.svg" />
        <input (keydown.enter)="onSearch()" [(ngModel)]="searchedKeyword" [placeholder]="placeholder" class="search-bar-input" type="text" />
      </div>
      <man-simple-button (click)="onSearch()" *ngIf="showSearchBar">OK</man-simple-button>
    </div>
    <ng-container *ngIf="filterOnSide">
      <ng-container *ngTemplateOutlet="formGroupTemplate"></ng-container>
    </ng-container>
  </div>

  <ng-container *ngIf="!filterOnSide">
    <ng-container *ngTemplateOutlet="formGroupTemplate"></ng-container>
  </ng-container>

  <ng-template #formGroupTemplate>
    <div [formGroup]="formGroup" [ngClass]="{ 'filter-on-side': filterOnSide }" class="form">
      <div [ngClass]="{ 'without-search': !showSearchBar, 'search-bar': !filterOnSide, 'filter-on-side': filterOnSide }" class="filters">
        <div *ngIf="showSorting" [ngClass]="{ 'search-bar-order': !filterOnSide }">
          <select (change)="onSearch()" class="search-bar-select" aria-label="Sorbarendezési beállítások" formControlName="sort">
            <option class="search-bar-select-option" value="date_desc">Legfrissebb elöl</option>
            <option class="search-bar-select-option" value="date_asc">Legrégebbi elöl</option>
          </select>
        </div>

        <button (click)="toggle()" *ngIf="showToggleButton" [class.active]="active" class="search-bar-btn">
          <span [class.active-color]="active" class="search-bar-btn-txt">Szűrés</span>
          <i [class.hide]="!active" class="icon icon-man-filter"></i>
          <i [class.hide]="active" class="icon icon-man-filter-black"></i>
        </button>
      </div>

      <div #filterWrapper [class.active]="active" [ngClass]="{ 'filter-on-side': filterOnSide }" class="search-filter">
        <img (click)="this.active = false" alt="Bezárás" class="search-filter-close-icon" src="assets/images/icons/close.svg" />
        <div class="search-filter-title">Szűrés a találatok között</div>
        <div class="filter-button">
          <man-simple-button (click)="filter()" [wide]="true">Szűrés</man-simple-button>
        </div>
        <div [ngClass]="isShowColumns && isShowContentTypes ? 'search-filter-settings' : 'search-filter-settings2'">
          <div class="search-filter-settings-column">
            <h4 class="search-filter-settings-title">Publikálás dátuma</h4>
            <div class="search-filter-published">
              <form *ngIf="formGroup" [formGroup]="formGroup">
                <ng-container *ngFor="let item of data?.datum; let last = last">
                  <label [for]="'datum_' + item.slug" class="search-filter-checkbox">
                    <div class="search-filter-checkbox-wrap">
                      <input
                        [id]="'datum_' + item.slug"
                        [value]="item.slug"
                        class="search-filter-checkbox-input"
                        formControlName="dateFilterType"
                        type="radio"
                      />
                      <span class="search-filter-checkbox-checkmark"></span>
                      <span class="search-filter-checkbox-text">
                        <man-date-range-picker
                          (onDateChange)="handleDateChange($event)"
                          *ngIf="last && isExactDate; else notLast"
                          [id]="'dateRangePicker'"
                          [isInline]="true"
                        >
                          {{ item.title }}
                        </man-date-range-picker>
                        <ng-template #notLast>
                          {{ item.title }}
                        </ng-template>
                      </span>
                    </div>
                  </label>
                </ng-container>
              </form>
            </div>
          </div>
          <div *ngIf="isShowColumns" class="search-filter-settings-column">
            <h4 class="search-filter-settings-title">Milyen rovatban keressen?</h4>
            <ng-container *ngFor="let item of data?.rovat; let i = index" formArrayName="selectedColumns">
              <label [for]="'rovat_' + item?.slug" class="search-filter-checkbox">
                <div class="search-filter-checkbox-wrap">
                  <input [formControlName]="i" [id]="'rovat_' + item?.slug" class="search-filter-checkbox-input" type="checkbox" />
                  <span class="search-filter-checkbox-checkmark"></span>
                  <span class="search-filter-checkbox-text">
                    {{ item.title }}
                  </span>
                </div>
              </label>
            </ng-container>
          </div>
          <div *ngIf="isShowContentTypes" class="search-filter-settings-column">
            <h4 class="search-filter-settings-title">Tartalom típusa</h4>
            <ng-container *ngFor="let item of data?.tipus; let i = index" formArrayName="selectedContentTypes">
              <label [for]="'tipus_' + item?.slug" class="search-filter-checkbox">
                <div class="search-filter-checkbox-wrap">
                  <input [formControlName]="i" [id]="'tipus_' + item?.slug" class="search-filter-checkbox-input" type="checkbox" />
                  <span class="search-filter-checkbox-checkmark"></span>
                  <span class="search-filter-checkbox-text">
                    {{ item.title }}
                  </span>
                </div>
              </label>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <div class="external-data-wrapper">
    <ng-content></ng-content>
  </div>
</div>
