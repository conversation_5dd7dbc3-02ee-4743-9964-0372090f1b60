import { ChangeDetectionStrategy, Component, DestroyRef, effect, ElementRef, inject, input, signal, viewChild } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { UtilService } from '@trendency/kesma-core';
import { ActivatedRoute } from '@angular/router';
import { PersonalizedRecommendationService } from '../../services';
import { ArticleCardType } from '../../definitions';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ManArticleCardComponent } from '../article-card/man-article-card.component';
import { StrossleAdvertComponent } from '../strossle-advert/strossle-advert.component';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-external-recommendations',
  templateUrl: './external-recommendations.component.html',
  styleUrls: ['./external-recommendations.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManArticleCardComponent, StrossleAdvertComponent, NgClass],
})
export class ExternalRecommendationsComponent {
  readonly #utilService = inject(UtilService);
  readonly #destroyRef = inject(DestroyRef);
  readonly #personalizedRecommendationService = inject(PersonalizedRecommendationService);
  readonly #route = inject(ActivatedRoute);

  readonly wide = input<boolean>(false);

  readonly routeData = toSignal(this.#route.data);
  readonly externalRecommendationsBlock = viewChild<ElementRef<HTMLDivElement>>('externalRecommendationsBlock');

  readonly externalRecommendations = signal<ArticleCard[]>([]);
  readonly ArticleCardType = ArticleCardType;

  constructor() {
    effect(() => {
      if (this.routeData() || !this.#utilService.isBrowser()) {
        this.getPersonalizedRecommendations();
      }
    });

    effect(() => {
      const externalRecommendations = this.externalRecommendationsBlock()?.nativeElement;

      if (!externalRecommendations || !this.#utilService.isBrowser()) {
        return;
      }

      if ('IntersectionObserver' in window) {
        this.#observeExternalRecommendations();
      }
    });
  }

  getPersonalizedRecommendations(): void {
    this.#personalizedRecommendationService
      .getPersonalizedRecommendations()
      .pipe(takeUntilDestroyed(this.#destroyRef))
      .subscribe((data: ArticleCard[]): void => this.externalRecommendations.set(data));
  }

  #observeExternalRecommendations(): void {
    if (!this.externalRecommendationsBlock()?.nativeElement) {
      return;
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting && this.externalRecommendations()) {
          this.#personalizedRecommendationService.sendPersonalizedRecommendationAv(this.externalRecommendations()).subscribe();
          observer.unobserve(this.externalRecommendationsBlock()!.nativeElement);
        }
      });
    });
    observer.observe(this.externalRecommendationsBlock()!.nativeElement);
  }
}
