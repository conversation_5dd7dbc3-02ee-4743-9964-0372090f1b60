<div #externalRecommendationsBlock class="recommendation-block recommendation-block-external" [ngClass]="{ wide: wide() }">
  @for (article of externalRecommendations(); let i = $index; track article.id) {
    <man-article-card [data]="article" [styleID]="ArticleCardType.ExternalRecommendation"></man-article-card>

    @if (i === (wide() ? 7 : 5)) {
      <div class="full-row">
        <app-strossle-advert advertId="Mandiner_normal_content_11"></app-strossle-advert>
      </div>
    }
  }
</div>
