@use 'shared' as *;

.recommendation-block {
  display: flex;
  flex-direction: column;
  grid-gap: 34px 16px;
  margin-bottom: 4px;
  margin-top: 36px;

  &-external {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 30px 20px;

    &.wide {
      grid-template-columns: repeat(4, 1fr);
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: repeat(1, 1fr) !important;
    }
  }

  .full-row {
    grid-column: 1 / -1;
  }
}
