<span class="heading-line-logo" *ngIf="data?.visibleIcon && !data?.icon">
  <i class="icon icon-logo-inverse"></i>
</span>
<ng-container *ngIf="!data?.url; else withUrl">
  <h2 class="heading-line-title">{{ data?.text }}</h2>
</ng-container>

<ng-template #withUrl>
  <a class="heading-line-link" *ngIf="!isExternal; else externalLink" [routerLink]="data?.url">
    <h2 class="heading-line-title">{{ data?.text }}</h2>
  </a>

  <div *ngIf="data?.urlName" class="heading-line-right">
    <a class="heading-line-right-more" [routerLink]="['/', data?.url]">{{ data?.urlName }}</a>
    <i class="icon icon-chevron-right"></i>
  </div>
</ng-template>

<ng-template #externalLink>
  <a class="heading-line-link" [href]="data?.url" target="_blank">
    <h2 class="heading-line-title">{{ data?.text }}</h2>
  </a>
</ng-template>
