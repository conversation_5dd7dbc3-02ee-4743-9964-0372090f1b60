@use 'shared' as *;

:root {
  .flatpickr {
    margin-top: 7px;

    &-custom-header {
      display: flex;
      align-items: center;
      margin: 10px 8px;
      padding: 0.5em;

      &-ranges {
        display: flex;
        justify-content: space-around;
        margin: 10px 0;

        &-item {
          width: 100%;
          margin: 0 10px;
          font-size: 14px;
          border-bottom: 1px solid var(--kui-gray-200);
          text-align: center;
          font-weight: bold;
          padding-bottom: 5px;
        }
      }

      &-date {
        flex: 1;
        height: 1em;
        font-weight: bold;
        text-align: center;
        font-size: 14px;
      }
    }

    &-custom-content {
      font-weight: bold;
      position: absolute;
      left: 0;
      right: 0;
      top: 57px;
    }
  }

  .flatpickr-calendar {
    .flatpickr-months {
      .flatpickr-prev-month,
      .flatpickr-next-month {
        top: 40px;
        margin: 5.5px;

        svg {
          width: 10px;
          height: 10px;

          &:hover {
            fill: var(--kui-orange-600);
          }
        }
      }
    }

    .flatpickr-innerContainer {
      .flatpickr-rContainer {
        .flatpickr-weekdays {
          .flatpickr-weekdaycontainer {
            .flatpickr-weekday {
              color: var(--kui-black);
              font-family: var(--kui-font-primary);
            }
          }
        }

        .flatpickr-days {
          .dayContainer {
            &:not(:has(.endRange)):not(:has(.inRange)) {
              .flatpickr-day.startRange::after {
                background-color: transparent;
              }
            }

            .flatpickr-day {
              font-family: var(--kui-font-primary);

              &.inRange {
                background-color: var(--kui-black);
                border: 1px solid var(--kui-black);
                color: var(--kui-white);
              }

              &.inRange:not(.startRange),
              &.inRange:not(.endRange) {
                box-shadow:
                  -5px 0 0 var(--kui-black),
                  5px 0 0 var(--kui-black);
              }

              &.endRange,
              &.startRange {
                background-color: var(--kui-orange-600);
                border-radius: 50%;
                border: none;
                position: relative;

                &::after {
                  content: '';
                  background-color: var(--kui-black);
                  display: block;
                  position: absolute;
                  top: 0;
                  width: 50%;
                  height: 100%;
                  right: 25px;
                  z-index: -1;
                }
              }

              &.startRange.endRange::after {
                background-color: transparent;
              }

              &.startRange::after {
                right: 0;
              }

              &.endRange:not(:nth-child(7n)):not(.startRange) {
                box-shadow: -5px 0 0 var(--kui-black);
              }

              &:nth-of-type(7n).endRange,
              &:nth-of-type(7n).inRange {
                box-shadow: -5px 0 0 var(--kui-black);
              }

              &:nth-of-type(7n + 1).inRange {
                box-shadow: 0 0 0 var(--kui-black);
              }

              &:nth-of-type(7n + 1).endRange:after {
                background-color: transparent;
              }

              &.endRange:not(:nth-child(7n)).endRange {
                box-shadow: none;
              }

              &.today {
                border: none;
              }
            }
          }
        }
      }
    }
  }
}
