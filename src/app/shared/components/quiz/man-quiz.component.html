<ng-container *ngFor="let currentQuestion of data?.questions; let questionIndex = index">
  <div class="quiz-step" *ngIf="questionIndex !== 0">{{ questionIndex + 1 }}. kérdés</div>
  <div class="quiz-question">
    <div class="quiz-question-container">
      <img loading="lazy" class="quiz-question-image" [src]="currentQuestion?.image || 'assets/images/placeholder-16-9.svg'" alt="Kvíz kérdést ábrázoló kép." />
      <h5 class="quiz-question-text">{{ currentQuestion.title }}</h5>
      <div class="overlay"></div>
    </div>
    <div class="answer-list">
      <ng-container *ngFor="let answer of currentQuestion.answers; let answerIndex = index">
        <input
          class="radio-input"
          type="radio"
          (change)="onSelectAnswer(questionIndex, answerIndex)"
          [name]="'answer_' + currentQuestion.id"
          [id]="'answer_' + currentQuestion.id + '_' + answerIndex"
          [disabled]="givenAnswers[questionIndex] !== undefined"
        />
        <label
          class="radio-label"
          [for]="'answer_' + currentQuestion.id + '_' + answerIndex"
          [ngClass]="[
            !answer.isCorrect && givenAnswers[questionIndex] !== undefined && givenAnswers[questionIndex] === answerIndex ? 'wrong' : '',
            answer.isCorrect && givenAnswers[questionIndex] !== undefined ? 'correct' : '',
          ]"
        >
          {{ answer.title }}
          <span class="extra-label correct">
            <span class="extra-label-text"> A helyes válasz</span>
            <i class="extra-label-icon icon icon-okay"></i>
          </span>
          <span class="extra-label wrong">
            <span class="extra-label-text">Rossz válasz</span>
            <i class="extra-label-icon icon icon-wrong"></i>
          </span>
        </label>
      </ng-container>
    </div>
  </div>
</ng-container>
<div class="quiz-question" *ngIf="rating">
  <div class="quiz-image-question">
    <img class="quiz-rating-image" *ngIf="ratingImage" loading="lazy" [src]="ratingImage" alt="" />
  </div>
  <div class="quiz-result">
    <div class="quiz-result-status">{{ correctCount }}/{{ data?.questions?.length }}</div>
    <div class="quiz-result-text">{{ rating.text }}</div>
    <div class="quiz-result-share">
      <i class="icon icon-share-fb"></i>
      <a (click)="onFBShareClick()"> Facebook megosztás </a>
    </div>
  </div>
</div>
