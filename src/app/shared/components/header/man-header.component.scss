@use 'shared' as *;

$userGoldIcon: '/assets/images/icons/icon-mandiner-user-gold.svg';
$userDefaultIcon: '/assets/images/icons/icon-mandiner-hamburger-user.svg';
$searchGoldIcon: '/assets/images/icons/icon-mandiner-search-hover.svg';

:host {
  display: block;
  padding-top: 35px;
}

.hide {
  display: none !important;
}

.empty-menu-item {
  pointer-events: none;
}

.header {
  background: var(--kui-white);
  color: var(--kui-black);
  font-family: var(--kui-font-primary);
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 55px;
  box-shadow: 0 5px 5px 0 #00000026;
  z-index: 9000;
  position: fixed;
  top: 0;
  left: 0;

  .minmax {
    text-align: right;
  }

  &-magazine {
    display: block;
    margin-top: 20px;
  }

  .divider {
    margin-bottom: 20px;
    display: block;
    background-color: var(--kui-gray-100);
    border: none;
    height: 1px;
  }

  &-flex-wrapper {
    display: flex;
    align-items: center;
  }

  &-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: calc(1310px + 24px); //12+12 padding
    width: 100%;
    margin: 0 auto;
    column-gap: 75px;
    padding: 5px 12px;
    min-height: 55px;
  }

  &-left {
    display: flex;
    align-items: center;

    @include media-breakpoint-down(md) {
      align-items: flex-start;
      width: 100%;
      justify-content: space-between;
    }
  }

  &-right {
    display: flex;
    width: 100%;

    @include media-breakpoint-down(md) {
      justify-content: end;
    }
  }

  &-current-date {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    white-space: nowrap;

    @include media-breakpoint-down(md) {
      &:not(.mobile) {
        display: none;
      }
    }

    &-day-name {
      margin-top: 5px;
      font-size: 10px;
      line-height: 10px;
    }
  }

  &-right-border {
    width: 1px;
    height: 16px;
    margin: 0 12.5px;
    background-color: var(--kui-orange-600);

    @include media-breakpoint-down(md) {
      &:not(.mobile) {
        display: none;
      }
    }
  }

  &-top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 13px;

    .header-exchange-rate {
      display: block;

      @include media-breakpoint-down(md) {
        // display: none;
      }
    }

    .header-dossier-tag-box {
      @include media-breakpoint-down(md) {
        display: none !important;
      }
    }
  }

  &-top-menu {
    @include media-breakpoint-down(lg) {
      display: none;
    }
  }

  &-top-menu-list {
    display: flex;
    gap: 10px;
    padding: 0 10px;
  }

  &-top-menu-list-item {
    border-right: 1px solid var(--kui-orange-600);
    padding-right: 10px;

    &:last-child {
      border-right: 0;
    }
  }

  &-top-menu-list-item-link {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: var(--kui-black);

    &:hover {
      text-decoration: underline;
      color: var(--kui-orange-600);
    }
  }

  &-bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &-end {
      justify-content: flex-end;
    }

    @include media-breakpoint-down(md) {
      justify-content: end;
    }

    .header-exchange-rate {
      @media screen and (min-width: 768px) and (max-width: 1199px) {
        display: block;
      }
    }
  }

  &-bottom-menu {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-bottom-menu-list {
    display: flex;
  }

  &-bottom-menu-list-item {
    position: relative;

    &:hover {
      z-index: 9000;
    }

    &:hover .header-bottom-menu-submenu {
      display: flex;
      flex-direction: column;
      z-index: 1;
      left: -20px;

      @media screen and (max-width: 1240px) {
        left: -10px;
      }
    }

    &.parent {
      &:hover .header-bottom-menu-list-item-link {
        z-index: 9000;

        &::after {
          content: '';
          position: absolute;
          top: calc(50% - 3.5px);
          right: 0;
          width: 12px;
          height: 7px;
          background-image: url('/assets/images/icons/icon-mandiner-chevron-up.svg');
        }
      }
    }
  }

  &-bottom-menu-list-item-link {
    position: relative;
    display: flex;
    column-gap: 4px;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    color: var(--kui-black);
    padding: 12px 24px 12px 0px;
    align-items: center;
    height: 100%;
    margin: 0;

    @media screen and (max-width: 1240px) {
      margin-left: 0px;
    }

    &:hover {
      text-decoration: underline;
    }

    &.active,
    &:hover {
      color: var(--kui-orange-600);
    }

    &.no-link {
      &:hover {
        @extend %default-text;
      }
    }
  }

  &-bottom-menu-submenu {
    display: none;
    position: absolute;
    top: 0%;
    left: 0;
    right: 0;
    padding: 40px 10px 5px 10px;
    box-shadow: var(--kui-shadow);
    background-color: var(--kui-gray-50);
    transition: 0.3s ease all;
    z-index: 0;
    min-width: 130px;

    @media screen and (max-width: 1240px) {
      padding: 30px 10px 0px 0px;
      min-width: 100px;
    }

    &-link {
      color: var(--kui-gray-4);
      padding: 10px;
      display: block;
      font-weight: 600;

      &:hover {
        background: var(--kui-gray-5);
        text-decoration: underline;
        color: var(--kui-orange-600);
      }

      &.no-link {
        &:hover {
          @extend %default-text;
        }
      }
    }

    &-sub {
      margin-left: 24px;

      &-link {
        font-weight: normal;

        &:hover {
          background: var(--kui-gray-5);
          text-decoration: underline;
          color: var(--kui-orange-600);
        }
      }
    }
  }

  &-logo-image {
    min-width: 149px;
    min-height: 30px;
    display: block;
    cursor: pointer;

    @include media-breakpoint-down(sm) {
      min-width: auto;
    }
  }

  &-subscription-cover {
    display: block;
    min-width: 82px;
    min-height: 114px;

    @include media-breakpoint-down(md) {
      min-width: 60px;
      min-height: 83px;
      width: 60px;
      height: 83px;
    }
  }

  &-exchange-rate {
    min-width: 85px;

    @include media-breakpoint-down(md) {
      display: none;
    }

    &-price {
      white-space: nowrap;
    }
  }

  &-exchange-rate-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    margin-top: 3px;
  }

  &-exchange-rate-text-box {
    min-width: 35px;
  }

  .weather {
    display: flex;
    align-items: center;
    color: var(--kui-black);
    font-size: 12px;
    gap: 5px;

    @include media-breakpoint-down(md) {
      &:not(.weather-mobile) {
        display: none;
      }
    }

    .negative-margin {
      margin-left: -4px;
    }

    .max {
      margin-bottom: 3px;
    }

    &-icon {
      width: 21px;
      height: 15.52px;
    }
  }

  .icon-mandiner-euro {
    min-width: 9px;
    min-height: 12px;
    margin-right: 5px;
  }

  .icon-mandiner-dollar {
    min-width: 9px;
    min-height: 14px;
    margin-right: 5px;
    margin-left: 0.5px;
  }

  .icon-mandiner-exchange-rate-up {
    min-width: 12px;
    min-height: 6px;
    margin-right: 5px;
  }

  .icon-mandiner-exchange-rate-down {
    min-width: 12px;
    min-height: 6px;
    margin-right: 5px;
  }

  .icon-mandiner-search {
    min-width: 26px;
    min-height: 27px;
  }

  .icon-mandiner-chevron-up {
    min-width: 12px;
    min-height: 7px;
  }

  .icon-mandiner-hamburger {
    min-width: 30px;
    min-height: 25px;
    cursor: pointer;
  }

  .icon-mandiner-user {
    min-width: 20px;
    min-height: 20px;
  }

  .icon-mandiner-hamburger-user {
    min-width: 16px;
    min-height: 16px;
  }

  .icon-mandiner-close {
    min-width: 12px;
    min-height: 12px;
  }

  .icon-mandiner-hamburger-close {
    min-width: 18px;
    min-height: 18px;
  }

  .icon-mandiner-hamburger-search {
    min-width: 20px;
    min-height: 20px;
  }

  .header-hamburger-menu-social .icon {
    min-width: 20px;
    min-height: 20px;
  }

  .icon-mandiner-youtube {
    min-width: 20px;
    min-height: 14px;
  }

  &-login-desktop {
    width: 44px;
    height: 44px;
    background-color: var(--kui-orange-600);
    position: relative;

    @include media-breakpoint-down(md) {
      width: 38px;
      height: 38px;
    }

    &-button {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      .chevron {
        display: none;
      }
    }

    &.logged-in {
      .header-login-desktop-button {
        padding: 14px 9px;
      }

      .chevron {
        display: block;
        margin-left: 2px;
        min-width: 10px;
        min-height: 7px;
        transform: rotate(180deg);
      }
    }

    &:hover:not(&.open) {
      background-color: var(--kui-gray-50);

      .icon-mandiner-hamburger-user {
        background-image: url($userGoldIcon);
      }

      .icon-mandiner-arrow-up-white {
        height: 10px;
        transform: rotate(90deg);
        background-image: url('/assets/images/icons/icon-mandiner-chevron-right-gold.svg');
      }
    }

    &.open {
      background-color: var(--kui-orange-600);

      .icon-mandiner-hamburger-user {
        background-image: url($userDefaultIcon);
      }
    }
  }

  &-hamburger-button {
    display: none;

    @include media-breakpoint-down(md) {
      display: inherit;
      margin-left: 20px;
    }
  }

  &-login-mobile {
    display: none;
    width: 44px;
    height: 44px;
    position: relative;

    &.logged-in {
      .header-login-mobile-button {
        padding: 14px 9px;
      }
    }

    @include media-breakpoint-down(md) {
      &.logged-in {
        .chevron {
          display: block;
          margin-left: 2px;
          min-width: 10px;
          min-height: 7px;
          transform: rotate(180deg);
        }

        &.open .chevron {
          transform: none;
        }
      }

      &:hover {
        background-color: var(--kui-gray-200);
      }
    }

    @include media-breakpoint-down(sm) {
      width: 38px;
      height: 38px;
      position: static;

      &-button {
        padding: 11px 11px;
      }

      &.logged-in {
        .header-login-mobile-button {
          padding: 11px 5px;
        }
      }
    }
  }

  &-search {
    background-color: var(--kui-black);
    margin-left: 20px;
    margin-right: 1px;
    height: 44px;
    width: 44px;
    cursor: pointer;

    &:hover {
      background-color: var(--kui-gray-50);

      .icon-mandiner-hamburger-search {
        background-image: url($searchGoldIcon);
      }
    }

    &.disabled {
      pointer-events: none;
      cursor: auto;
    }

    &.active-input {
      width: 100%;
      background-color: unset;
      padding: 0;
    }

    @include media-breakpoint-down(md) {
      margin-left: 0;
    }

    @include media-breakpoint-down(sm) {
      padding: 10px 10px;
      width: 38px;
      height: 38px;
    }

    &-button-desktop {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 44px;
      min-width: 44px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-button-mobile {
      display: none;

      @include media-breakpoint-down(md) {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
      }
    }
  }

  &-search-bar {
    display: block;
    height: 42px;
  }

  &-search-bar-form {
    display: flex;
    align-items: center;
    margin-top: 10px;
    background-color: var(--kui-white);
    color: var(--kui-black);
    border-right: 1px solid var(--kui-gray-200);
    border-bottom: 1px solid var(--kui-gray-200);
    border-left: 1px solid var(--kui-gray-200);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
    padding: 10px;
    text-transform: uppercase;
    position: relative;
    z-index: 100;
    transform-origin: top center;
    transform: scaleY(0);
    transition: all 0.2s ease;
    opacity: 0;
    height: 0;
    width: 452px;

    &.active {
      opacity: 1;
      width: 900px;
      height: 50px;
      transform: translate(-855px, -10px);
    }

    &-input {
      height: 48px;
      width: 850px;
      font-size: 16px;
      margin-left: 10px;
    }

    &-btn {
      position: relative;
      left: 10px;
    }
  }

  &-search-bar-close {
    display: flex;
    padding: 0 17px;
    height: 42px;
    cursor: pointer;
  }

  &-search-dropdown {
    background-color: var(--kui-white);
    color: var(--kui-black);
    border-right: 1px solid var(--kui-gray-200);
    border-bottom: 1px solid var(--kui-gray-200);
    border-left: 1px solid var(--kui-gray-200);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
    padding: 10px 52px;
    text-transform: uppercase;
    position: relative;
    z-index: 100;
    width: 100%;
    transform-origin: top center;
    transform: scaleY(0);
    transition: all 0.2s ease;
    opacity: 0;
    height: 0;

    &.active {
      height: auto;
      opacity: 1;
      transform: scaleY(1);
    }
  }

  &-search-recommended-expressions {
    .header-search-dropdown-list-item-link {
      font-size: 16px;
    }
  }

  &-search-recommended-articles {
    .header-search-dropdown-list-item-link {
      font-size: 14px;
      margin-bottom: 6px;
    }

    .header-search-dropdown-list-item {
      border-bottom: 1px solid var(--kui-gray-100);
    }
  }

  &-search-dropdown-title {
    font-weight: 400;
    font-size: 13px;
    line-height: 18px;
    color: var(--kui-gray-400);
    text-transform: uppercase;
    margin: 0;
  }

  &-search-dropdown-list {
    margin-bottom: 21px;
  }

  &-search-dropdown-list-item {
    display: flex;
    flex-direction: column;
    padding: 5px 0;
  }

  &-search-dropdown-list-item-link {
    text-transform: none;
    font-weight: 700;
    line-height: 22px;
    color: var(--kui-black);
    cursor: default;

    &:hover {
      color: var(--kui-orange-600);
      background-color: var(--kui-gray-50);
    }
  }

  &-search-dropdown-list-item-date {
    font-weight: 400;
    font-size: 10px;
    line-height: 15px;
    color: var(--kui-gray-400);
  }

  &-date-box {
    display: none;
    font-weight: 400;
    font-size: 12px;
    line-height: 25px;

    @include media-breakpoint-down(md) {
      display: block;
    }
  }

  &-hamburger-bottom {
    display: none;
  }

  &-hamburger-menu {
    display: none;
    position: fixed;
    overflow-x: scroll;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: var(--kui-white);

    &.open {
      display: block;
    }

    &-link {
      display: block;
      margin: 10px 0 0 20px;
      color: var(--kui-black);
    }
  }

  &-hamburger-menu-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &-hamburger-menu-left {
    width: 100%;
    padding: 20px;

    .header-exchange-rate {
      display: inline-block;
      margin-bottom: 30px;

      &.mobile {
        margin-bottom: 0;
      }
    }
  }

  &-hamburger-menu-sidebar {
    width: 60px;
    background: var(--kui-orange-600);
    box-shadow: inset 4px 4px 4px rgba(0, 0, 0, 0.25);
  }

  &-hamburger-menu-login-box {
    display: flex;
    justify-content: center;
    box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
    height: 50px;
  }

  &-hamburger-menu-close {
    margin: 0 0 16px 6px;
  }

  &-hamburger-menu-search {
    display: flex;
    width: 100%;
  }

  &-hamburger-menu-search-form {
    display: flex;
    justify-content: space-between;
    height: 43px;
    border: 1px solid var(--kui-gray-100);
    padding: 10px;
    align-items: center;

    .icon-mandiner-search {
      min-width: 17px;
      min-height: 17px;
    }
  }

  &-hamburger-menu-search-button {
    text-align: left;
    margin-right: 15px;
    display: flex;
  }

  &-hamburger-menu-input {
    width: 100%;
    margin-right: 5px;
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    color: var(--kui-black);

    &::placeholder {
      color: rgba(0, 0, 0, 0.5);
      font-weight: 300;
    }
  }

  &-hamburger-menu-title {
    font-family: var(--kui-font-family);
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin-top: 20px;
    color: var(--kui-black);
    margin-bottom: 10px;
  }

  &-hamburger-menu-list {
    margin-bottom: 30px;
  }

  &-hamburger-menu-list-item {
    border-bottom: 1px solid var(--kui-gray-100);
    padding: 10px 0;
  }

  &-hamburger-menu-list-item-link {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: var(--kui-black);
    cursor: pointer;

    &.active + .header-hamburger-bottom {
      display: block;
    }
  }

  .header-hamburger-bottom-sub {
    .header-hamburger-menu-link {
      margin-left: 32px;
    }
  }

  &-hamburger-menu-secondary-list {
    border-bottom: 1px solid var(--kui-gray-50);
    margin-bottom: 22px;
  }

  &-hamburger-menu-secondary-list-item {
    padding-bottom: 10px;
  }

  &-hamburger-menu-secondary-list-item-link {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: var(--kui-black);
  }

  &-hamburger-menu-social {
    display: flex;
    column-gap: 24px;
    margin-bottom: 94px;
  }

  &-trending-tag-box {
    margin: 20px 0;
  }

  .dossier-list {
    margin-bottom: 20px;
  }

  &-user-menu {
    position: absolute;
    right: 0;
    top: 44px;
    min-width: 240px;
    box-shadow: 0 4px 4px rgb(0 0 0 / 25%);
    background-color: var(--kui-orange-600);
    transition: 0.3s ease all;
    padding: 20px;
    z-index: 11;

    &.mobile {
      @include media-breakpoint-down(sm) {
        top: 48px;
        width: 100%;
      }
    }

    &-item-link {
      padding: 9px 0;
      display: block;
      transition: 0.3s ease all;
      font-weight: 700;
      color: var(--kui-white);
      border-bottom: 1px solid var(--kui-gray-50);
      line-height: 24px;

      &:first-child {
        padding-top: 0;
      }

      &:hover {
        color: var(--kui-black);
      }
    }

    &-backdrop {
      z-index: 10;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      cursor: auto;
    }
  }
}

%default-text {
  cursor: default;
  text-decoration: none;
  color: var(--kui-black);
}
