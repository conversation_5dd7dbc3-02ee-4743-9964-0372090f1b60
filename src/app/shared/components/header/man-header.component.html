<header class="header">
  <div class="header-wrapper">
    <div class="header-left">
      <a (click)="logoClick()" [routerLink]="['/']" class="header-logo">
        <img alt="Mandiner logó" class="header-logo-image" height="30" src="/assets/images/new-mandiner-logo.svg" width="150" />
      </a>
    </div>

    <div class="header-right">
      <div [class.header-bottom-end]="hideElement" class="header-bottom">
        <div [ngClass]="{ hide: hideElement }" class="header-bottom-menu">
          <ul class="header-bottom-menu-list">
            <li *ngFor="let menuItem of mainMenu; let index = index" [ngClass]="{ parent: menuItem?.children?.length }" class="header-bottom-menu-list-item">
              <ng-container *ngIf="isHomePage && menuItem.relatedType === 'HomePage'; else link">
                <ng-container *ngTemplateOutlet="homePageLink; context: { item: menuItem, index: index }"></ng-container>
              </ng-container>
              <ng-template #link>
                <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? normalLink : customUrl; context: { item: menuItem, index: index }"> </ng-container>
              </ng-template>
              <ul *ngIf="menuItem.children?.length" class="header-bottom-menu-submenu">
                <li *ngFor="let subMenuItem of menuItem.children">
                  <ng-container *ngIf="subMenuItem.relatedType === RelatedType.DROPDOWN; else notEmpty">
                    <span class="header-bottom-menu-submenu-link no-link">{{ subMenuItem.title }}</span>
                  </ng-container>
                  <ng-template #notEmpty>
                    <ng-container
                      *ngTemplateOutlet="!subMenuItem.isCustomUrl ? normalLink : customUrl; context: { item: subMenuItem, isSubmenu: true, index: index }"
                    ></ng-container>
                  </ng-template>
                  <ul *ngIf="subMenuItem.children?.length" class="header-bottom-menu-submenu-sub">
                    <li *ngFor="let subSubMenuItem of subMenuItem.children">
                      <ng-container
                        *ngTemplateOutlet="
                          !subSubMenuItem.isCustomUrl ? normalLink : customUrl;
                          context: { item: subSubMenuItem, isSubsubmenu: true, index: index }
                        "
                      >
                      </ng-container>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </div>

        <div class="header-flex-wrapper">
          <div class="header-exchange-rate">
            <div class="header-exchange-rate-row">
              <i class="icon icon-mandiner-euro"></i>
              <i class="icon icon-mandiner-exchange-rate-{{ data?.exchangeRateEuro?.direction ?? 'down' }}"></i>
              <span class="header-exchange-rate-price">{{ data?.exchangeRateEuro?.price }} Ft</span>
            </div>

            <div class="header-exchange-rate-row">
              <i class="icon icon-mandiner-dollar"></i>
              <i class="icon icon-mandiner-exchange-rate-{{ data?.exchangeRateEuro?.direction ?? 'down' }}"></i>
              <span class="header-exchange-rate-price">{{ data?.exchangeRateDollar?.price }} Ft</span>
            </div>
          </div>

          <div class="header-right-border"></div>

          <a *ngIf="currentWeather" class="weather" href="https://koponyeg.hu" target="_blank">
            <div class="actual">
              <i [class]="currentWeather?.icon2" class="icon weather-icon"></i>
            </div>
            <div class="minmax">
              <div [class.negative-margin]="+currentWeather.maxTemperature < 0" class="max">{{ currentWeather.maxTemperature }}°C</div>
              <div [class.negative-margin]="+currentWeather.minTemperature < 0" class="min">{{ currentWeather.minTemperature }}°C</div>
            </div>
          </a>

          <div class="header-right-border"></div>

          <div class="header-flex-wrapper">
            <div [ngClass]="{ open: isHamburgerMenuOpen }" class="header-hamburger-menu">
              <div class="header-hamburger-menu-container">
                <div class="header-hamburger-menu-left">
                  <div (click)="closeHamburgerMenu()" class="header-hamburger-menu-close">
                    <i class="icon icon-mandiner-hamburger-close"></i>
                  </div>

                  <div class="header-hamburger-menu-search">
                    <div class="header-hamburger-menu-search-form">
                      <button class="header-hamburger-menu-search-button">
                        <i class="icon icon-mandiner-search"></i>
                      </button>
                      <input
                        (focusout)="closeSearchDropdown()"
                        (keydown.enter)="onSearch()"
                        [(ngModel)]="searchPhrase"
                        class="header-hamburger-menu-input"
                        placeholder="Keresett kifejezés..."
                        type="text"
                      />
                    </div>
                    <div class="search-btn">
                      <man-simple-button (click)="onSearch()">OK</man-simple-button>
                    </div>
                  </div>

                  <ul class="header-hamburger-menu-list">
                    <li *ngFor="let menuItem of mainMenu; let index = index" class="header-hamburger-menu-list-item">
                      <ng-container *ngIf="isHomePage && menuItem.relatedType === 'HomePage'; else link">
                        <ng-container *ngTemplateOutlet="hamburgerHomePageLink; context: { item: menuItem, index: index }"></ng-container>
                      </ng-container>

                      <ng-template #link>
                        <ng-container
                          *ngTemplateOutlet="!menuItem.isCustomUrl ? hamburgerNormalLink : hamburgerCustomUrl; context: { item: menuItem, index: index }"
                        >
                        </ng-container>
                      </ng-template>
                      <ul *ngIf="menuItem.children?.length" class="header-hamburger-bottom">
                        <li *ngFor="let subMenuItem of menuItem.children">
                          <ng-container *ngIf="subMenuItem.relatedType === RelatedType.DROPDOWN; else notEmpty">
                            <span class="header-hamburger-menu-link no-link">{{ subMenuItem.title }}</span>
                          </ng-container>
                          <ng-template #notEmpty>
                            <a (click)="closeHamburgerMenu()" [routerLink]="subMenuItem.link" class="header-hamburger-menu-link">
                              {{ subMenuItem.title }}
                            </a>
                          </ng-template>
                          <ul class="header-hamburger-bottom-sub">
                            <li *ngFor="let subSubMenuItem of subMenuItem.children">
                              <a (click)="closeHamburgerMenu()" [routerLink]="subSubMenuItem.link" class="header-hamburger-menu-link">
                                {{ subSubMenuItem.title }}
                              </a>
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </li>
                  </ul>

                  <div class="header-flex-wrapper mobile">
                    <div class="header-exchange-rate mobile">
                      <div class="header-exchange-rate-row">
                        <i class="icon icon-mandiner-euro"></i>
                        <i class="icon icon-mandiner-exchange-rate-{{ data?.exchangeRateEuro?.direction ?? 'down' }}"></i>
                        <span class="header-currency-price-euro">{{ data?.exchangeRateEuro?.price }} Ft</span>
                      </div>

                      <div class="header-exchange-rate-row">
                        <i class="icon icon-mandiner-dollar"></i>
                        <i class="icon icon-mandiner-exchange-rate-{{ data?.exchangeRateEuro?.direction ?? 'down' }}"></i>
                        <span class="header-currency-price-dollar">{{ data?.exchangeRateDollar?.price }} Ft</span>
                      </div>
                    </div>

                    <div class="header-right-border mobile"></div>

                    <a (click)="closeHamburgerMenu()" *ngIf="currentWeather" class="weather weather-mobile" href="https://koponyeg.hu" target="_blank">
                      <div class="actual">
                        <i [class]="currentWeather?.icon2" class="icon weather-icon"></i>
                      </div>
                      <div class="minmax">
                        <div [class.negative-margin]="+currentWeather.maxTemperature < 0" class="max">{{ currentWeather.maxTemperature }}°C</div>
                        <div [class.negative-margin]="+currentWeather.minTemperature < 0" class="min">{{ currentWeather.minTemperature }}°C</div>
                      </div>
                    </a>

                    <div class="header-right-border mobile"></div>

                    <div class="header-current-date mobile">
                      {{ currentDate }}
                      <div class="header-current-date-day-name">
                        {{ currentDayName }}
                      </div>
                    </div>
                  </div>

                  <div class="header-trending-tag-box">
                    <man-trending-tags (tagClicked)="closeHamburgerMenu()" [data]="trendingTags"></man-trending-tags>
                  </div>

                  <h3 class="header-hamburger-menu-title">Akták</h3>

                  <man-dossier-list (dossierClicked)="closeHamburgerMenu()" [data]="dossiers" class="dossier-list"></man-dossier-list>
                  <man-subscription-strip (subscriptionClicked)="closeHamburgerMenu()" text="Előfizetés"></man-subscription-strip>

                  <a (click)="closeHamburgerMenu()" [routerLink]="authorsLink" class="header-hamburger-menu-link"> Szerzőink </a>
                  <a (click)="closeHamburgerMenu()" [routerLink]="newsletterLink" class="header-hamburger-menu-link"> Feliratkozás hírlevélre </a>

                  <hr class="divider" />

                  <div class="header-hamburger-menu-social">
                    <a (click)="closeHamburgerMenu()" [href]="facebookLink | bypass: 'url'" class="icon icon-mandiner-facebook"></a>
                    <a (click)="closeHamburgerMenu()" [href]="instagramLink | bypass: 'url'" class="icon icon-mandiner-instagram"></a>
                    <a (click)="closeHamburgerMenu()" [href]="twitterLink | bypass: 'url'" class="icon icon-mandiner-twitter"></a>
                    <a (click)="closeHamburgerMenu()" [href]="linkedInLink | bypass: 'url'" class="icon icon-mandiner-linkedin"></a>
                    <a (click)="closeHamburgerMenu()" [href]="youtubeLink | bypass: 'url'" class="icon icon-mandiner-youtube"></a>
                  </div>
                </div>

                <div class="header-hamburger-menu-sidebar">
                  <div class="header-hamburger-menu-login-box">
                    <i class="icon icon-mandiner-hamburger-user"></i>
                  </div>
                </div>
              </div>
            </div>

            <div class="header-current-date">
              {{ currentDate }}
              <div class="header-current-date-day-name">
                {{ currentDayName }}
              </div>
            </div>

            <div [class.active-input]="hideElement" [ngClass]="{ disabled: isSearchDisabled }" class="header-search">
              <button (mouseup)="toggleSearchBar()" [ngClass]="{ hide: hideElement }" class="header-search-button-desktop" aria-label="Kereső">
                <i class="icon icon-mandiner-hamburger-search"></i>
              </button>

              <button (click)="openHamburgerMenu()" class="header-search-button-mobile" aria-label="Kereső">
                <i class="icon icon-mandiner-hamburger-search"></i>
              </button>

              <div [class.hide]="(isSearchBarOpen$ | async) === false" class="header-search-bar">
                <div #headerSearchBar [class.active]="isSearchBarOpen$ | async" class="header-search-bar-form">
                  <i #iconMandinerSearch (click)="onSearch()" class="icon icon-mandiner-search"></i>
                  <input
                    (keydown.enter)="onSearch()"
                    [(ngModel)]="searchPhrase"
                    class="header-search-bar-form-input"
                    placeholder="Keresett kifejezés..."
                    type="text"
                  />
                  <man-simple-button (click)="onSearch()" [styleInfo]="{ button: { height: '49px' } }" class="header-search-bar-form-btn"
                    >OK
                  </man-simple-button>
                </div>
              </div>
            </div>

            <div [ngClass]="{ hide: hideElement, open: isUserMenuOpen, 'logged-in': !!user }" class="header-login-desktop">
              <div (click)="handleUserMenuClick()" class="header-login-desktop-button">
                <i class="icon icon-mandiner-hamburger-user"></i>
                <i class="icon icon-mandiner-arrow-up-white chevron"></i>
              </div>
              <div *ngIf="isUserMenuOpen" class="header-user-menu">
                <ng-container *ngTemplateOutlet="userMenuContent"></ng-container>
              </div>
              <div (click)="isUserMenuOpen = !isUserMenuOpen" *ngIf="isUserMenuOpen" class="header-user-menu-backdrop"></div>
            </div>
          </div>
        </div>

        <div [ngClass]="{ hide: hideElement, open: isUserMenuOpen, 'logged-in': !!user }" class="header-login-mobile">
          <div (click)="handleUserMenuClick()" class="header-login-mobile-button">
            <i class="icon icon-mandiner-hamburger-user"></i>
            <i [class.opened]="isUserMenuOpen" class="icon icon-mandiner-arrow-up-white chevron"></i>
          </div>
          <div *ngIf="isUserMenuOpen" class="header-user-menu mobile">
            <ng-container *ngTemplateOutlet="userMenuContent"></ng-container>
          </div>
          <div (click)="isUserMenuOpen = !isUserMenuOpen" *ngIf="isUserMenuOpen" class="header-user-menu-backdrop"></div>
        </div>

        <div (click)="openHamburgerMenu()" class="header-hamburger-button">
          <i class="icon icon-mandiner-hamburger"></i>
        </div>
      </div>
    </div>
  </div>
  <ng-content select="[eb-header]"></ng-content>
  <ng-content select="[olimpia-header]"></ng-content>
</header>

<ng-template #userMenuContent>
  <a (click)="isUserMenuOpen = !isUserMenuOpen" class="header-user-menu-item-link" routerLink="/profil">Személyes profil</a>
  <a (click)="redirectToSubscriptions()" class="header-user-menu-item-link">Előfizetés</a>
  <a (click)="isUserMenuOpen = !isUserMenuOpen" class="header-user-menu-item-link" routerLink="/profil/beallitasok">Beállítások</a>
  <a (click)="handleLogout()" class="header-user-menu-item-link">Kilépés</a>
</ng-template>

<ng-template #homePageLink let-index="index" let-item="item">
  <a
    (click)="activateItem(index)"
    [class.empty-menu-item]="item?.children?.length"
    [ngClass]="{ active: index === activeItem }"
    [target]="item.target"
    class="header-bottom-menu-list-item-link"
    href="/"
  >
    {{ item.title }}
  </a>
</ng-template>

<ng-template #normalLink let-index="index" let-isSubmenu="isSubmenu" let-isSubsubmenu="isSubsubmenu" let-item="item">
  <ng-container *ngIf="item.relatedType === RelatedType.DROPDOWN; else notEmpty">
    <span
      [ngClass]="{
        active: index === activeItem,
        'header-bottom-menu-list-item-link': !isSubmenu && !isSubsubmenu,
        'header-bottom-menu-submenu-sub-link': isSubsubmenu,
        'header-bottom-menu-submenu-link': isSubmenu,
      }"
      class="no-link"
      >{{ item.title }}</span
    >
  </ng-container>
  <ng-template #notEmpty>
    <a
      (click)="activateItem(index)"
      [ngClass]="{
        active: index === activeItem,
        'header-bottom-menu-list-item-link': !isSubmenu && !isSubsubmenu,
        'header-bottom-menu-submenu-sub-link': isSubsubmenu,
        'header-bottom-menu-submenu-link': isSubmenu,
      }"
      [routerLink]="item.link"
      [target]="item.target"
    >
      {{ item.title }}
    </a>
  </ng-template>
</ng-template>
<ng-template #customUrl let-index="index" let-isSubmenu="isSubmenu" let-isSubsubmenu="isSubsubmenu" let-item="item">
  <a
    (click)="activateItem(index)"
    [href]="item.link"
    [ngClass]="{
      active: index === activeItem,
      'header-bottom-menu-list-item-link': !isSubmenu && !isSubsubmenu,
      'header-bottom-menu-submenu-sub-link': isSubsubmenu,
      'header-bottom-menu-submenu-link': isSubmenu,
    }"
    [target]="item.target"
  >
    {{ item.title }}
  </a>
</ng-template>

<ng-template #hamburgerHomePageLink let-index="index" let-item="item">
  <a [ngClass]="{ active: index === activeItem }" [target]="item.target" class="header-hamburger-menu-list-item-link" href="/">
    {{ item.title }}
  </a>
</ng-template>

<ng-template #hamburgerNormalLink let-index="index" let-item="item">
  <ng-container *ngIf="item.relatedType === RelatedType.DROPDOWN; else notEmpty">
    <button (click)="activateItem(index)" [ngClass]="{ active: index === activeItem }" class="header-hamburger-menu-list-item-link no-link">
      {{ item.title }}
    </button>
  </ng-container>
  <ng-template #notEmpty>
    <button
      (click)="activateItem(index); onHamburgerLinkClicked(item)"
      [ngClass]="{ active: index === activeItem }"
      class="header-hamburger-menu-list-item-link"
    >
      {{ item.title }}
    </button>
  </ng-template>
</ng-template>
<ng-template #hamburgerCustomUrl let-index="index" let-item="item">
  <a
    (click)="activateItem(index); closeHamburgerMenu()"
    [href]="item.link"
    [ngClass]="{ active: index === activeItem }"
    [target]="item.target"
    class="header-hamburger-menu-list-item-link"
  >
    {{ item.title }}
  </a>
</ng-template>
