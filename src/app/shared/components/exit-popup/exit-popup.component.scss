@use 'shared' as *;

:host {
  .exit-popup {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9100;

    &-backdrop {
      background-color: rgba(#323232, 0.8);
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9101;
    }

    &-wrapper {
      background-color: var(--kui-white);
      padding: 32px;
      z-index: 9102;
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 20px;
      max-width: 1080px;
      width: 80%;
      max-height: 98%;
      overflow: hidden;

      .btn-close {
        background: var(--kui-orange-600);
        position: absolute;
        top: 16px;
        right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;

        &:hover {
          background-color: black;
        }

        i {
          min-width: 14px;
          min-height: 14px;
        }
      }

      .exit-popup-header {
        font-family: var(--kui-font-primary);
        line-height: 24px;
        color: var(--kui-orange-600);
        padding-bottom: 20px;
        border-bottom: 1px solid var(--kui-gray-150);
        text-align: start;
        width: 65%;
        white-space: nowrap;
      }

      .exit-popup-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .article-card {
          display: flex;
          flex-direction: column;
          flex-basis: calc(25% - 15px);
          gap: 10px;
          cursor: pointer;

          &-title {
            font-family: var(--kui-font-primary);
            font-size: 18px;
            font-weight: 600;
            line-height: 26px;
            letter-spacing: 0em;
            text-align: left;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
  }
}
