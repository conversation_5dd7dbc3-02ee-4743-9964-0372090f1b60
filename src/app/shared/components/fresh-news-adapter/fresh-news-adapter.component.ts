import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ArticleCard, AutoFill, BackendArticleSearchResult, backendDateToDate, convertDateToBackendUtcDate } from '@trendency/kesma-ui';
import { ApiService } from '../../services';
import { subHours } from 'date-fns';
import { ManTwentyfourHourNewsComponent } from '../twentyfour-hour-news/man-twentyfour-hour-news.component';

@Component({
  selector: 'app-fresh-news-adapter',
  templateUrl: './fresh-news-adapter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ManTwentyfourHourNewsComponent],
})
export class FreshNewsAdapterComponent implements OnInit {
  articles: ArticleCard[] = [];

  @Input() autoFill: AutoFill;

  constructor(
    private readonly apiService: ApiService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (!this.autoFill) {
      return;
    }

    const tagSlugs = this.filterByKey(this.autoFill?.filterTags as [], 'slug');
    const columnSlugs = this.filterByKey(this.autoFill?.filterColumns as [], 'slug');
    const priorityIds = this.filterByKey(this.autoFill?.filterPriorities as [], 'id');

    this.getArticlesByLastDay(columnSlugs, tagSlugs, priorityIds);
  }

  private getArticlesByLastDay(columnSlugs: string[], tagSlugs: string[], priorityId: string[]): void {
    const lastDay = subHours(new Date(), 24);

    // BE expects the date format in ISO 8601, but without any timezone.
    // For example: from_date=2023-01-17T11:00:00&to_date=2023-01-17T12:00:00
    const lastDayString = convertDateToBackendUtcDate(lastDay);
    const nowString = convertDateToBackendUtcDate(new Date());

    this.apiService
      .searchByKeyword('', 'desc', lastDayString, nowString, ['article', 'articleVideo'], columnSlugs, 0, 500, '', tagSlugs, priorityId)
      .subscribe((result) => {
        if (!result.data?.length) {
          return;
        }
        this.articles = result.data?.map((article: BackendArticleSearchResult) => ({
          ...article,
          publishDate: backendDateToDate(article?.publishDate as string),
        })) as any as ArticleCard[];
        this.changeDetectorRef.markForCheck();
      });
  }

  private filterByKey(data: any[], key: string): string[] {
    return data?.map((value) => value?.[key]) || [];
  }
}
