@use 'shared' as *;

:host {
  background: var(--kui-gray-50);
  border-bottom: 5px solid var(--kui-orange-600);
  border-top: 5px solid var(--kui-orange-600);
  font-family: var(--kui-font-primary);
  padding: 20px;
  margin: auto;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  @include media-breakpoint-down(md) {
    margin: auto -20px;
  }
}
.subscription-block {
  &-title {
    font-size: 16px;
    line-height: 22px;
  }
  &-btn {
    background: var(--kui-black);
    padding: 12px;
    color: var(--kui-orange-600);
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    display: block;
    width: 100%;
  }
  &-member {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    &-login {
      color: var(--kui-orange-600);
    }
  }
}
