@use 'shared' as *;

.block-content::ng-deep {
  .raw-html-embed {
    //Migrated contents sometimes have absolute iframe.
    position: relative;
    min-height: 400px;
  }
  a {
    color: var(--kui-orange-600);
    font-weight: normal;
  }
  figure.image {
    margin-bottom: 24px;
    max-width: 100%; //Migrated contents sometimes have an explicit width which would cause overflow.
    img {
      // This hack is needed, because migrated content from the old mandiner site has explicit width and heights for all the images.
      // When visiting the site on a smaller screen, images can look deformed, because they don't keep their original aspect ratio.
      height: auto !important;
    }
    &.image-style-align-left {
      position: relative;
      margin-left: 20px;
      &:before {
        content: '';
        position: absolute;
        left: -20px;
        top: 0;
        width: 2px;
        height: 100%;
        background: var(--kui-orange-600);
      }
    }
    &.image-style-align-right {
      position: relative;
      margin-right: 20px;
      &:before {
        content: '';
        position: absolute;
        right: -20px;
        top: 0;
        width: 2px;
        height: 100%;
        background: var(--kui-orange-600);
      }
    }
    &.image-border {
      border: 1px solid var(--kui-orange-600);
      padding: 20px;
    }
    figcaption {
      font-size: 12px;
      line-height: 18px;
      max-width: 800px;
      margin: auto;
      display: table-caption;
      caption-side: bottom;
      &:before {
        content: none;
      }
    }
  }

  .custom-text-style {
    // Migrált kiemelések fix.
    font-size: 0;
    display: inline-flex;
    margin-top: 0;

    &.underlined-text {
      text-decoration: underline;
      text-decoration-thickness: 1px;
      display: inline-block;
    }
    &.highlight {
      background: var(--kui-orange-600);
      color: var(--kui-white);
      a {
        color: var(--kui-blue-300);
      }
      padding: 20px;
      font-size: 18px;
      line-height: 24px;
      font-weight: 600;
      margin-bottom: 24px;
      text-transform: uppercase;
      .highlightBlock-content::after {
        content: ' ';
        width: 100px;
        height: 1px;
        display: block;
        background: var(--kui-white);
        margin-top: 5px;
      }
    }
    &.highlight-style2 {
      background: var(--kui-gray-50);
      padding: 20px;
      font-size: 18px;
      line-height: 24px;
      margin-bottom: 24px;
      font-weight: 800;
      .highlightBlock-style2-content {
        position: relative;
        padding-left: 20px;
        p:last-of-type {
          margin-bottom: 0;
        }
        &::before {
          content: ' ';
          position: absolute;
          left: 0;
          top: 10%;
          height: 80%;
          width: 2px;
          background: var(--kui-orange-600);
        }
      }
    }
    &.quote {
      // Migrált cikkek idézet fix.
      display: inline-flex;
      margin-top: 0;
      font-size: 0;

      position: relative;
      border: 0;
      color: var(--kui-orange-600);

      @include media-breakpoint-down(md) {
        margin-top: -20px;
        margin-bottom: 20px;
      }
      &-block {
        margin-bottom: 24px;
      }
      &:before {
        content: '\201d';
        position: absolute;
        margin-top: 30px;
        left: 10px;
        font-size: 120px;
        font-style: normal;
        font-family: var(--kui-font-playfair);
      }
      .quoteBlock-content {
        position: relative;
        margin-left: 40px;
        p {
          font-weight: 700;
          font-style: italic;
          font-size: 22px;
          line-height: 28px;

          span {
            font-size: 22px;
          }
        }
      }
      .quoteBlock-content p:before,
      .quoteBlock-content p:after {
        content: none;
      }
    }
    &.border-text {
      border: 1px solid var(--kui-orange-600);
      background: none;
      margin-bottom: 24px;
      .borderBlock-content::after {
        content: ' ';
        width: 100px;
        height: 1px;
        display: block;
        background: var(--kui-black);
        margin-top: 5px;
      }
    }
  }
  div.keretes {
    position: relative;
    border: 0;
    color: var(--kui-orange-600);
    font-weight: 700;
    font-style: italic;
    font-size: 22px;
    line-height: 28px;
    min-height: 70px;
    margin-bottom: 15px;
    display: flex;

    &:before {
      display: block;
      content: '\201d';
      font-family: var(--kui-font-secondary);
      float: left;
      margin-top: 30px;
      margin-right: 20px;
      font-size: 120px;
      font-style: normal;
    }
  }
  div.aranykeretes {
    margin: 15px 0;
    border: 1px solid var(--kui-orange-600);
    padding: 20px;
    line-height: 1.5;
  }
  div.aranykeretes2 {
    border: 1px solid var(--kui-orange-600);
    padding: 20px;
    font-size: 18px;
    line-height: 24px;
    font-weight: 600;
    text-transform: uppercase;
    margin: 15px auto;
    width: fit-content;
    .image {
      // For migrated image inside aranykeretes2.
      margin-bottom: 0 !important;
    }
  }

  p {
    margin-bottom: 24px;
  }

  h2,
  h3,
  h4 {
    margin-bottom: 24px;
    font-weight: bold;
  }

  h2 {
    font-size: 30px;
    line-height: 29px;
    @include media-breakpoint-down(sm) {
      font-size: 26px;
      line-height: 24px;
    }
  }

  h3 {
    font-size: 24px;
    line-height: 23px;
    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 19px;
    }
  }

  h4 {
    font-size: 18px;
    text-decoration: none;
    line-height: 23px;
    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 19px;
    }
  }

  li > .underlined-text {
    margin: 0 !important;
  }

  ul {
    @include unordered-list();
  }

  ol {
    @include ordered-list();
  }

  figure.table {
    display: table;
    overflow: hidden;

    table tr,
    td {
      background-color: transparent;
    }

    figcaption {
      background-color: var(--kui-gray-75);
      display: table-caption;
      caption-side: top;
      text-align: left;
      padding: 0.6em;
      font-size: 0.75em;
    }
  }
}
