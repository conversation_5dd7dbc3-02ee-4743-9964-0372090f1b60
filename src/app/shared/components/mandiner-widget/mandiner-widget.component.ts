import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe, NgIf } from '@angular/common';
import { AdvertService } from '../../services/advert.service';
import { Observable, Subject, takeUntil } from 'rxjs';
import { StorageService, UtilService } from '@trendency/kesma-core';

@Component({
  selector: 'app-mandiner-widget',
  templateUrl: './mandiner-widget.component.html',
  styleUrls: ['./mandiner-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe],
})
export class MandinerWidgetComponent implements OnInit, OnDestroy {
  @Input() widgetId: string;
  hasDebug: boolean;
  isAdEnabled$: Observable<boolean>;
  isAdEnabled: boolean = true;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly advertService: AdvertService,
    private readonly elementRef: ElementRef,
    private readonly cdr: ChangeDetectorRef,
    private readonly storageService: StorageService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    if (!this.utilsService.isBrowser()) return;

    this.hasDebug = this.storageService.getLocalStorageData('apptest') as boolean;
    this.isAdEnabled$ = this.advertService.enableAdsSubject.asObservable();

    this.advertService.enableAdsSubject.pipe(takeUntil(this.destroy$)).subscribe((isEnabled: boolean) => {
      this.isAdEnabled = isEnabled;

      if (!isEnabled) {
        this.hideAndClearWidgetContent();
      } else {
        this.loadWidget();
      }

      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load the Mandiner widget by triggering the _mgc.load function
   */
  private loadWidget(): void {
    if (typeof window !== 'undefined' && (window as any)._mgq) {
      (window as any)._mgq.push(['_mgc.load']);
    }
  }

  /**
   * Hides the component and clears any widget content that might have been injected
   */
  private hideAndClearWidgetContent(): void {
    const element = this.elementRef.nativeElement;

    if (element) {
      element.style.display = 'none';

      // Clear the widget container
      const widgetContainer = element.querySelector(`[data-widget-id="${this.widgetId}"]`);
      if (widgetContainer) {
        widgetContainer.innerHTML = '';

        const scripts = widgetContainer.querySelectorAll('script');
        scripts.forEach((script: Element) => script.remove());

        const iframes = widgetContainer.querySelectorAll('iframe');
        iframes.forEach((iframe: Element) => iframe.remove());

        (widgetContainer as HTMLElement).style.display = 'none';
      }

      // Remove any Mandiner widget related elements
      const widgetElements = element.querySelectorAll('[data-type="_mgwidget"], [data-widget-id]');
      widgetElements.forEach((widgetElement: Element) => {
        if (widgetElement !== widgetContainer) {
          widgetElement.remove();
        }
      });
    }
  }
}
