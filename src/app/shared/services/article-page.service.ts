import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import {
  ApiResponseMeta,
  ApiResult,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  BackendArticle,
  previewBackendArticleToArticleCard,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { backendArticleSocialToSocial, backendArticlesToArticles } from '../utils';
import { BackendArticleSocial } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(private readonly reqService: ReqService) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    return this.reqService
      .get<ApiResult<BackendArticle, ApiResponseMeta>>(`/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}`, {
        params: {
          previewType,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${(month + '').padStart(2, '0')}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: backendArticlesToArticles(data),
        meta,
      }))
    );
  }

  getArticleSocialData(article: ApiResult<Article>): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticleSocial>>(`/content-page/article/${article.data.id}/social-count`).pipe(
      map((result: ApiResult<BackendArticleSocial>) => {
        return {
          data: {
            ...article.data,
            ...backendArticleSocialToSocial(result.data),
          },
          meta: article.meta,
        };
      })
    );
  }

  // prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
  //   return (body ?? []).map((bodyPart: ArticleBody) => ({
  //     ...bodyPart,
  //     details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
  //       ...detail,
  //       ...this.prepareArticleBodyDetail(detail, bodyPart.type),
  //     })),
  //   }));
  // }

  prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    const advertIndex = 2;
    return body.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.prepareArticleBodyDetail(detail, bodyPart.type),
      })),
      ...(bodyPart.type === ArticleBodyType.Advert && {
        advert: `Mandiner_normal_content_${advertIndex}`,
      }),
    }));
  }

  //   prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
  //     if (type === ArticleBodyType.Article) {
  //       return {
  //         ...detail,
  //         value: { ...previewBackendArticleToArticleCard(detail.value), ...{ label: { text: 'Ezt is ajánljuk a témában' } } },
  //       };
  //     }
  //     return { ...detail };
  //   }
  // }

  prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
}
