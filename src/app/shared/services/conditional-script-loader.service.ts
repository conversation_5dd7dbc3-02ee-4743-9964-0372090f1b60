import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class ConditionalScriptLoaderService {
  private readonly targetUrl = '/hirek/2008/04/trackback-teszteles';
  private scriptsLoaded = false;

  constructor(
    private readonly router: Router,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {
    this.initializeRouterListener();
  }

  private initializeRouterListener(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      this.handleNavigation(event.url);
    });

    this.handleNavigation(this.router.url);
  }

  private handleNavigation(url: string): void {
    console.debug('[ConditionalScriptLoader] Navigation to:', url, 'Target:', this.targetUrl);

    if (url === this.targetUrl) {
      console.debug('[ConditionalScriptLoader] Target page detected, loading scripts');
      this.loadScripts();
    } else {
      console.debug('[ConditionalScriptLoader] Not target page, removing scripts');
      this.removeScripts();
    }
  }

  private loadScripts(): void {
    if (this.scriptsLoaded || !this.utilsService.isBrowser()) {
      return;
    }

    const existingHtmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    const existingLoaderScript = this.document.querySelector('script[src="/assets/scripts/html-load-loader.js"]');

    if (existingHtmlLoadScript && existingLoaderScript) {
      this.scriptsLoaded = true;
      return;
    }

    if (!existingHtmlLoadScript) {
      const htmlLoadScript = this.document.createElement('script');
      htmlLoadScript.async = true;
      htmlLoadScript.id = 'WVrQZqComZ';
      htmlLoadScript.setAttribute('data-sdk', 'l/1.1.11');
      htmlLoadScript.setAttribute('data-cfasync', 'false');
      htmlLoadScript.src = 'https://html-load.com/loader.min.js';
      htmlLoadScript.setAttribute('charset', 'UTF-8');
      htmlLoadScript.setAttribute('data', 'irrron6o4fhugojo4o2o4hch8hto8jhe2h4h8oso7o4ojfogh8h4oj5h4h4fo7o4hefogovh4oso7o4fh4oifuhyojojo7o4qhcuh8hto8jqo4kh9');
      htmlLoadScript.setAttribute(
        'onload',
        // eslint-disable-next-line max-len, quotes
        "!async function(){let e='html-load.com';const t=window,r=e=>new Promise((t=>setTimeout(t,e))),o=t.atob,a=t.btoa,s=r=>{const s=o('VGhlcmUgd2FzIGEgcHJvYmxlbSBsb2FkaW5nIHRoZSBwYWdlLiBQbGVhc2UgY2xpY2sgT0sgdG8gbGVhcm4gbW9yZS4=');if(confirm(s)){const o=new t.URL('https://report.error-report.com/modal'),s=o.searchParams;s.set('url',a(location.href)),s.set('error',a(r.toString())),s.set('domain',e),location.href=o.href}else location.reload()};try{const c=()=>new Promise((e=>{let r=Math.random().toString(),o=Math.random().toString();t.addEventListener('message',(e=>e.data===r&&t.postMessage(o,'*'))),t.addEventListener('message',(t=>t.data===o&&e())),t.postMessage(r,'*')}));async function n(){try{let e=!1,o=Math.random().toString();if(t.addEventListener('message',(t=>{t.data===o+'_as_res'&&(e=!0)})),t.postMessage(o+'_as_req','*'),await c(),await r(500),e)return!0}catch(e){console.error(e)}return!1}const i=[100,500,1e3];for(let l=0;l<=i.length&&!await n();l++){if(l===i.length-1)throw o('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA')+e+o('IGlzIHRhaW50ZWQuIFBsZWFzZSBhbGxvdyA')+e;await r(i[l])}}catch(d){console.error(d);try{t.localStorage.setItem(t.location.host+'_fa_'+a('last_bfa_at'),Date.now().toString())}catch(m){}try{setInterval((()=>document.querySelectorAll('link,style').forEach((e=>e.remove()))),100);const h=await(await fetch('https://error-report.com/report?type=loader_light&url='+a(location.href)+'&error='+a(d),{method:'POST'})).text();let g=!1;t.addEventListener('message',(e=>{'as_modal_loaded'===e.data&&(g=!0)}));let p=document.createElement('iframe');const f=new t.URL('https://report.error-report.com/modal'),u=f.searchParams;u.set('url',a(location.href)),u.set('eventId',h),u.set('error',a(d)),u.set('domain',e),p.src=f.href,p.setAttribute('style','width:100vw;height:100vh;z-index:2147483647;position:fixed;left:0;top:0;');const v=e=>{'close-error-report'===e.data&&(p.remove(),t.removeEventListener('message',v))};t.addEventListener('message',v),document.body.appendChild(p);const w=()=>{const e=p.getBoundingClientRect();return'none'!==t.getComputedStyle(p).display&&0!==e.width&&0!==e.height},S=setInterval((()=>{if(!document.contains(p))return clearInterval(S);w()||(s(d),clearInterval(S))}),1e3);setTimeout((()=>{g||s(errStr)}),3e3)}catch(y){s(y)}}}();"
      );
      htmlLoadScript.setAttribute(
        'onerror',
        // eslint-disable-next-line max-len, quotes
        "!async function(){const e=window,t=e.atob,r=e.btoa;let o=JSON.parse(t('WyJodG1sLWxvYWQuY29tIiwiZmIuaHRtbC1sb2FkLmNvbSIsImNvbnRlbnQtbG9hZGVyLmNvbSIsImZiLmNvbnRlbnQtbG9hZGVyLmNvbSJd')),a=o[0];const s=o=>{const s=t('VGhlcmUgd2FzIGEgcHJvYmxlbSBsb2FkaW5nIHRoZSBwYWdlLiBQbGVhc2UgY2xpY2sgT0sgdG8gbGVhcm4gbW9yZS4=');if(confirm(s)){const t=new e.URL('https://report.error-report.com/modal'),s=t.searchParams;s.set('url',r(location.href)),s.set('error',r(o.toString())),s.set('domain',a),location.href=t.href}else location.reload()};try{if(void 0===e.as_retry&&(e.as_retry=0),e.as_retry>=o.length)throw t('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA')+a+t('IGlzIGJsb2NrZWQuIFBsZWFzZSBhbGxvdyA')+a;const r=document.querySelector('#WVrQZqComZ'),s=document.createElement('script');for(let e=0;e<r.attributes.length;e++)s.setAttribute(r.attributes[e].name,r.attributes[e].value);const n=new e.URL(r.getAttribute('src'));n.host=o[e.as_retry++],s.setAttribute('src',n.href),r.setAttribute('id',r.getAttribute('id')+'_'),r.parentNode.insertBefore(s,r),r.remove()}catch(t){console.error(t);try{e.localStorage.setItem(e.location.host+'_fa_'+r('last_bfa_at'),Date.now().toString())}catch(e){}try{setInterval((()=>document.querySelectorAll('link,style').forEach((e=>e.remove()))),100);const o=await(await fetch('https://error-report.com/report?type=loader_light&url='+r(location.href)+'&error='+r(t),{method:'POST'})).text();let n=!1;e.addEventListener('message',(e=>{'as_modal_loaded'===e.data&&(n=!0)}));let c=document.createElement('iframe');const i=new e.URL('https://report.error-report.com/modal'),l=i.searchParams;l.set('url',r(location.href)),l.set('eventId',o),l.set('error',r(t)),l.set('domain',a),c.src=i.href,c.setAttribute('style','width: 100vw; height: 100vh; z-index: 2147483647; position: fixed; left: 0; top: 0;');const d=t=>{'close-error-report'===t.data&&(c.remove(),e.removeEventListener('message',d))};e.addEventListener('message',d),document.body.appendChild(c);const m=()=>{const t=c.getBoundingClientRect();return'none'!==e.getComputedStyle(c).display&&0!==t.width&&0!==t.height},h=setInterval((()=>{if(!document.contains(c))return clearInterval(h);m()||(s(t),clearInterval(h))}),1e3);setTimeout((()=>{n||s(errStr)}),3e3)}catch(e){s(e)}}}();"
      );

      this.document.head.appendChild(htmlLoadScript);
    }

    if (!existingLoaderScript) {
      const loaderScript = this.document.createElement('script');
      loaderScript.src = '/assets/scripts/html-load-loader.js';
      this.document.head.appendChild(loaderScript);
    }

    this.scriptsLoaded = true;
    console.debug('[ConditionalScriptLoader] Scripts loaded for target page:', this.targetUrl);
  }

  private removeScripts(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    // Remove the html-load.com script
    const htmlLoadScript = this.document.getElementById('tQNIlYwsPFK');
    if (htmlLoadScript) {
      htmlLoadScript.remove();
      console.debug('[ConditionalScriptLoader] Removed html-load script');
    }

    const loaderScripts = this.document.querySelectorAll('script[src="/assets/scripts/html-load-loader.js"]');
    loaderScripts.forEach((script) => {
      script.remove();
      console.debug('[ConditionalScriptLoader] Removed loader script');
    });

    const duplicateScripts = this.document.querySelectorAll('script[id^="tQNIlYwsPFK"]');
    duplicateScripts.forEach((script) => script.remove());

    this.scriptsLoaded = false;
  }
}
