import { Injectable } from '@angular/core';
import { ReqService, UtilService } from '@trendency/kesma-core';
import { EnvironmentApiUrl } from '@trendency/kesma-core/lib/definitions/environment.definitions';
import {
  ApiResponseMetaList,
  ApiResult,
  Article,
  ArticleCard,
  BackendComment,
  BackendCommentWithArticle,
  FollowedColumn,
  ReactionsData,
  ThumbnailImage,
  UserCommentsType,
} from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { AlertConfig, PublicUserCommentCount, UserStats } from '../../feature/profile/user-comments/api/user-comments.definitions';
import { environment } from '../../../environments/environment';
import { BackendProfileEditRequest } from '../../feature/profile/profile.definitions';
import { AuthorData, BackendUserResponse, ISearchParams, Judgement, RegistrationFormData } from '../definitions';
import { registrationFormDataToBackendRequest } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class SecureApiService {
  constructor(
    private readonly reqService: ReqService,
    private readonly utilService: UtilService
  ) {}

  get secureApiUrl(): string {
    if (typeof environment.secureApiUrl === 'string') {
      return environment.secureApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = environment.secureApiUrl as EnvironmentApiUrl;
    return this.utilService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getCurrentUser(): Observable<BackendUserResponse> {
    return this.reqService.get(`${this.secureApiUrl}/portal-user`);
  }

  editCurrentUser(requestData: BackendProfileEditRequest): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/save`, requestData);
  }

  editUserConsent(newsletter: boolean, marketingLetter: boolean): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/save-marketing-letter`, {
      newsletter,
      marketingLetter,
    });
  }

  finishRegister(formData: RegistrationFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/register-finish`, registrationFormDataToBackendRequest(formData));
  }

  logout(): Observable<void> {
    return this.reqService.get(`${this.secureApiUrl}/logout`);
  }

  deleteAccount(passwordOld: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/delete-account`, { password: passwordOld });
  }

  getArticleBody(columnSlug: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get(`${this.secureApiUrl}/articles/paywall/body/${columnSlug}/${year}/${month}/${articleSlug}`);
  }

  getMyVotesFor(
    id: string,
    params: object,
    userId: string,
    type: 'article' | 'comment' = 'article'
  ): Observable<ApiResult<ReactionsData, ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<ReactionsData, ApiResponseMetaList>>(
      `${this.secureApiUrl}/comments${type === 'comment' ? `/portal-user/${userId}` : ''}/${type}/${id}/my-votes`,
      {
        params,
      }
    );
  }

  getMyArticleComments(params: object, userId: string): Observable<ApiResult<UserCommentsType[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<UserCommentsType[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/my-article-comments`, {
      params,
    });
  }

  getMyComments(params: object, userId: string): Observable<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendCommentWithArticle[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/my-comments`, {
      params,
    });
  }

  getArticleComments(params: object, userId: string): Observable<ApiResult<UserCommentsType[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<UserCommentsType[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/article-comments`, {
      params,
    });
  }

  getCommentsCount(userId: string): Observable<ApiResult<PublicUserCommentCount, ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<PublicUserCommentCount, ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/comments-count`);
  }

  getCommentsFor(id: string, params: object, type: 'article' | 'comment' = 'article'): Observable<ApiResult<BackendComment[], ApiResponseMetaList>> {
    const urlSuffix = type === 'article' ? 'comments' : 'answers';
    return this.reqService.get<ApiResult<BackendComment[], ApiResponseMetaList>>(`${this.secureApiUrl}/comments/${type}/${id}/${urlSuffix}`, { params });
  }

  submitCommentFor(id: string, type: 'article' | 'comment' = 'article', text: string): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/${type}/${id}/create`, { text });
  }

  editComment(id: string, text: string): Observable<ApiResult<never>> {
    return this.reqService.patch<ApiResult<never>>(`${this.secureApiUrl}/comments/comment/${id}/update`, { text });
  }

  voteComment(id: string, vote: 'like' | 'dislike' | 'clear-like-dislike' | 'report'): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/comment/${id}/${vote}`, {});
  }

  clearJudgement(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/articles/article/${articleId}/clear-judgement`, {});
  }

  getJudgement(articleId: string): Observable<ApiResult<Judgement>> {
    return this.reqService.get<ApiResult<never>>(`${this.secureApiUrl}/articles/article/${articleId}/has-judgement`);
  }

  getReadingHistory(
    page = 0,
    itemsPerPage = 12,
    sort?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    material_types_only?: string
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ISearchParams = {
      rowCount_limit: itemsPerPage?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': sort === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ?? [],
      'columnSlugs[]': column ?? [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;

    return this.reqService
      .get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`${this.secureApiUrl}/read-history/read-articles`, {
        params,
      })
      .pipe(
        map((apiResult) => ({
          meta: apiResult.meta,
          data: apiResult.data?.map((article: ArticleCard) => ({
            ...article,
            thumbnail: {
              url: (article.thumbnail as unknown as string) ?? '',
              // BE returns thumbnail as string instead of object here, unlike in other places
            } as ThumbnailImage,
            isPaywalled: article.isPaywalled ? !!+article.isPaywalled : false,
            hasGallery: article.hasGallery ? !!+article.hasGallery : false,
            isVideoType: article.isVideo ? !!+article.isVideo : false,
          })),
        }))
      );
  }

  getSavedArticlesList(
    page = 0,
    itemsPerPage = 12,
    sort?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    material_types_only?: string
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ISearchParams = {
      rowCount_limit: itemsPerPage?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': sort === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ?? [],
      'columnSlugs[]': column ?? [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;
    return this.reqService
      .get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`${this.secureApiUrl}/saved-articles/list`, {
        params,
      })
      .pipe(
        map((apiResult) => ({
          meta: apiResult.meta,
          data: apiResult.data?.map((article: ArticleCard) => ({
            ...article,
            thumbnail: {
              url: (article.thumbnail as unknown as string) ?? '',
              // BE returns thumbnail as string instead of object here, unlike in other places
            } as ThumbnailImage,
            isPaywalled: article.isPaywalled ? !!+article.isPaywalled : false,
            hasGallery: article.hasGallery ? !!+article.hasGallery : false,
          })),
        }))
      );
  }

  getFollowedCategories(page = 0, itemsPerPage = 9999): Observable<ApiResult<FollowedColumn[], ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/followed-columns/list`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getFollowedAuthors(page = 0, itemsPerPage = 9999): Observable<ApiResult<AuthorData[], ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/followed-authors/list`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  postReadHistory(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.post(`${this.secureApiUrl}/read-history/article/${articleId}/mark-as-read`, {});
  }

  getSavedArticle(articleId: string): Observable<ApiResult<{ saved: boolean }>> {
    return this.reqService.get(`${this.secureApiUrl}/saved-articles/${articleId}`, {});
  }

  getMyStats(): Observable<ApiResult<UserStats>> {
    return this.reqService.get<ApiResult<UserStats>>(`${this.secureApiUrl}/likes-dislikes/count`);
  }

  getAlertConfig(): Observable<ApiResult<AlertConfig>> {
    return this.reqService.get<ApiResult<AlertConfig>>(`${this.secureApiUrl}/profile-config`);
  }

  updateAlertConfig(config: Partial<AlertConfig>): Observable<never> {
    return this.reqService.patch<never>(`${this.secureApiUrl}/profile-config`, config);
  }

  postSavedArticles(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.post(`${this.secureApiUrl}/saved-articles/save/${articleId}`, {});
  }

  deleteSavedArticles(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.delete(`${this.secureApiUrl}/saved-articles/delete/${articleId}`, {});
  }

  postReaction(articleId: string, liked: boolean): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/articles/article/${articleId}/${liked ? 'like' : 'dislike'}`, {});
  }

  getSavedArticleCount(): Observable<ApiResult<{ savedArticleCount: number }>> {
    return this.reqService.get(`${this.secureApiUrl}/saved-articles/count`, {});
  }

  getReadArticleCount(): Observable<ApiResult<{ readArticlesCount: number }>> {
    return this.reqService.get(`${this.secureApiUrl}/read-history/count`, {});
  }

  getMyCommentCount(): Observable<ApiResult<{ commentCount: number }>> {
    return this.reqService.get(`${this.secureApiUrl}/comments/portal-user/my-comments-count`, {});
  }

  legacyCancelRecurringSubscription(): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/subscription/recurring/cancel`, null);
  }

  setFollowedColumn(columnId: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/followed-columns/save/${columnId}`, null);
  }

  deleteFollowedColumn(columnId: string): Observable<void> {
    return this.reqService.delete(`${this.secureApiUrl}/followed-columns/delete/${columnId}`);
  }

  setFollowedAuthor(authorId: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/followed-authors/save/${authorId}`, null);
  }

  deleteFollowedAuthor(authorId: string): Observable<void> {
    return this.reqService.delete(`${this.secureApiUrl}/followed-authors/delete/${authorId}`);
  }
}
