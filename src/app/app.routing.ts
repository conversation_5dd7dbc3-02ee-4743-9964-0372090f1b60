import { Route, Routes, UrlMatchResult, UrlSegment } from '@angular/router';
import { BaseComponent, Error404Component, InitResolver, AuthGuard, VignetteAdService } from './shared';
import { NewsletterConfirmPageComponent } from './feature/newsletter/newsletter-confirm-page/newsletter-confirm-page.component';
import { NewsletterSuccessPageComponent } from './feature/newsletter/newsletter-success-page/newsletter-success-page.component';
import { olympicsAthleteProfileRoutes } from './feature/olympics/olympics-athlete-profile/olympics-athlete-profile.routing';
import { CheckRedirectBefore404Guard } from '@trendency/kesma-ui';

export const appRoutes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.layoutEditorRoutes),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    children: (
      [
        {
          path: '',
          pathMatch: 'full',
          loadChildren: () => import('./feature/home/<USER>').then((m) => m.homeRoutes),
        },
        // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
        {
          path: 'assets/:file',
          redirectTo: '404',
        },
        {
          path: 'assets/:dir/:file',
          redirectTo: '404',
        },
        {
          path: 'script/:file',
          redirectTo: '404',
        },
        {
          path: 'regisztracio',
          loadChildren: () => import('./feature/registration/registration.routing').then((m) => m.registrationRouting),
        },
        {
          path: 'bejelentkezes',
          loadChildren: () => import('./feature/login/login.routing').then((m) => m.loginRouting),
        },
        {
          path: 'elfelejtett-jelszo',
          loadChildren: () => import('./feature/forgot-password/forgot-password.routing').then((m) => m.forgotPasswordRouting),
        },
        {
          path: 'kijelentkezes',
          canActivate: [AuthGuard],
          loadChildren: () => import('./feature/logout/logout.routing').then((m) => m.logoutRouting),
        },
        {
          path: 'profil',
          canActivate: [AuthGuard],
          loadChildren: () => import('./feature/profile/profile.routing').then((m) => m.profileRouting),
        },
        {
          path: 'szerzo',
          loadChildren: () => import('./feature/author-page/author.routing').then((m) => m.authorRoutes),
        },
        {
          path: 'galeriak',
          loadChildren: () => import('./feature/galleries-page/galleries-page.routing').then((m) => m.galleriesPageRoutes),
        },
        {
          path: 'galeria', // Képnézegető
          loadChildren: () => import('./feature/gallery-layer/galleries-layer.routing').then((m) => m.galleryLayerRoutes),
        },
        {
          path: 'kovetett-szerzoim',
          loadChildren: () => import('./feature/followed-authors/followed-authors.routing').then((m) => m.followedAuthorsRoutes),
        },
        {
          path: 'velemeny',
          pathMatch: 'full',
          loadChildren: () => import('./feature/opinions/opinions-routing').then((m) => m.opinionsRouting),
        },
        {
          path: 'cimke',
          loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.tagsPageRoutes),
        },
        {
          // old url from redirect
          path: 'akta/:slug',
          loadChildren: () => import('./feature/dossier/dossier.routing').then((m) => m.dossierRoutes),
        },
        {
          path: 'hirfolyam/:articleSlug',
          loadChildren: () => import('./feature/news-feed/news-feed.routing').then((m) => m.newsFeedRouting),
        },
        {
          path: 'dosszie/:slug',
          loadChildren: () => import('./feature/dossier/dossier.routing').then((m) => m.dossierRoutes),
        },
        {
          path: 'hetilap',
          loadChildren: () => import('./feature/journal/journal.routing').then((m) => m.journalRouting),
        },
        {
          path: 'file/:fileId/:fileName',
          loadChildren: () => import('./feature/file/file.routing').then((m) => m.fileRoutes),
        },
        {
          path: 'elrendezes-elonezet/:layoutHash',
          loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.layoutPreviewRoutes),
        },
        {
          path: 'cikk-elonezet/:previewHash',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.staticPageRoutes),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: 'cikk-elonezet/:previewHash/:previewType',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.staticPageRoutes),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: 'rovat/:categorySlug',
          loadChildren: () => import('./feature/category/category.routing').then((m) => m.categoryRoutes),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          path: 'kereses',
          loadChildren: () => import('./feature/search-page/search-page.routing').then((m) => m.searchPageRoutes),
        },
        {
          path: 'video',
          loadChildren: () => import('./feature/video-podcast-list-page/video-podcast-list-page.routing').then((m) => m.videoPodcastListPageRoutes),
        },
        {
          path: 'hirek',
          loadChildren: () => import('./feature/news-list-page/news-list-page.routing').then((m) => m.newsListPageRoutes),
        },
        {
          path: 'dossziek',
          loadChildren: () => import('./feature/dossiers/dossiers.routing').then((m) => m.dossiersRoutes),
        },
        {
          path: 'hirlevel-feliratkozas',
          loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.newsletterRoutes),
          pathMatch: 'full',
        },
        {
          path: 'hirlevel-feliratkozas-megerositese',
          component: NewsletterConfirmPageComponent,
          pathMatch: 'full',
        },
        {
          path: 'hirlevel-feliratkozas-megerosites-sikeres',
          component: NewsletterSuccessPageComponent,
          data: { pageTextSuffix: 'feliratkozás' },
          pathMatch: 'full',
        },
        {
          path: 'hirlevel-leiratkozas',
          component: NewsletterSuccessPageComponent,
          data: { pageTextSuffix: 'leiratkozás' },
          pathMatch: 'full',
        },
        {
          path: 'mandiner-java',
          redirectTo: '/hirek',
        },
        {
          path: 'alapko-tartalom',
          loadChildren: () => import('./feature/foundation-content/foundation-content.routing').then((m) => m.foundationContentRouting),
        },
        // Olimpia
        {
          path: 'olimpia-2024/menetrend',
          loadChildren: () => import('./feature/olympics-schedules/olympics-schedules.routing').then((m) => m.olympicsSchedulesRoutes),
        },
        {
          path: 'olimpia-2024/dicsosegtabla',
          loadChildren: () => import('./feature/olympics-national-medals/olympics-national-medals.routing').then((m) => m.olympicsNationalMedalsRoutes),
        },
        {
          path: 'olimpia-2024/eremtablazat',
          loadChildren: () => import('./feature/olympics-medal-table/olympics-medal-table.routing').then((m) => m.OlympicsMedalTableRoutes),
        },
        {
          path: 'olimpia-2024/magyar-csapat',
          loadChildren: () => import('./feature/olympics-hungarian-team/olympics-hungarian-team.routing').then((m) => m.OlympicsHungarianTeamRoutes),
        },
        ...olympicsAthleteProfileRoutes,
        {
          path: '404',
          component: Error404Component,
          canActivate: [CheckRedirectBefore404Guard],
        },
        {
          // Hír
          path: ':categorySlug/:year/:month/:articleSlug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.staticPageRoutes),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          // Hír
          path: ':categorySlug/:articleSlug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.staticPageRoutes),
          data: {
            omitGlobalPageView: true,
          },
        },
        {
          matcher: (url: UrlSegment[]): UrlMatchResult | null => {
            const match = url[0].path.match(/\.[^.]+$/);
            if (match) {
              return {
                consumed: url,
                posParams: {},
              };
            }
            return null;
          },
          redirectTo: '404',
        },
        {
          path: ':slug',
          loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.staticPageRoutes),
        },
        {
          path: '**',
          redirectTo: '404',
        },
      ] as Routes
    ).map((route: Route) => ({ ...route, canDeactivate: [...(route.canDeactivate ?? []), VignetteAdService] })),
  },
];
