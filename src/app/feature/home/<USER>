import { Inject, Injectable, Optional } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { LayoutApiData, LayoutService } from '@trendency/kesma-ui';
import { catchError, map, tap } from 'rxjs/operators';
import { makeMobileLayout, searchLayoutContentElements, CacheService } from '../../shared';
import { REQUEST, UtilService } from '@trendency/kesma-core';
import { HttpParams } from '@angular/common/http';

const LAYOUT_CACHE_KEY = 'layoutData';

@Injectable()
export class HomeResolver {
  constructor(
    private readonly layoutService: LayoutService,
    private readonly router: Router,
    private readonly cache: CacheService,
    private readonly utils: UtilService,
    @Inject(REQUEST) @Optional() private readonly request: Request
  ) {}

  resolve(): Observable<LayoutApiData> {
    if (this.cache.has(LAYOUT_CACHE_KEY)) {
      return of(this.cache.get(LAYOUT_CACHE_KEY) as LayoutApiData);
    }

    return this.requestHomePage().pipe(
      tap((layoutData) => {
        this.cache.set(LAYOUT_CACHE_KEY, layoutData, 300_000);
      })
    );
  }

  private requestHomePage(): Observable<LayoutApiData> {
    let isSsrMobile = !!(this.request?.headers as unknown as Record<string, string>)?.['user-agent']?.match(/android|mobile|ios/i);

    if (this.utils.isBrowser() && !isSsrMobile) {
      isSsrMobile = window.innerWidth < 900;
    }

    const params: HttpParams = new HttpParams({
      fromObject: { 'fields[]': ['foundationTagSlug', 'foundationTagTitle'] },
    });

    return this.layoutService.getHomePage(isSsrMobile ? { isMobile: true, ...params } : params).pipe(
      catchError((err) => {
        this.router.navigate(['/', '500'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => {
        const struct = isSsrMobile ? [makeMobileLayout(searchLayoutContentElements(data.struct))] : data.struct;

        return {
          ...data,
          struct, // Struct will be shown before angular is loaded
        };
      })
    );
  }
}
