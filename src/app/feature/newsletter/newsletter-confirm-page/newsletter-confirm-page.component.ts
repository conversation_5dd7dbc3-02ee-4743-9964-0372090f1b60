import { Component, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { ActivatedRoute } from '@angular/router';
import { defaultMetaInfo, createMandinerTitle } from '../../../shared';

@Component({
  selector: 'app-newsletter-confirm-page',
  templateUrl: './newsletter-confirm-page.component.html',
  styleUrls: ['./newsletter-confirm-page.component.scss'],
})
export class NewsletterConfirmPageComponent implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute
  ) {}

  userEmail: string | null;

  ngOnInit(): void {
    const title = createMandinerTitle('Hírlevél megerősítés');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
    this.userEmail = this.route.snapshot?.queryParamMap?.get('email');
  }
}
