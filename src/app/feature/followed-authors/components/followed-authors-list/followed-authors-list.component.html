<man-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON><PERSON> szerz<PERSON>im' }]"></man-breadcrumb>

<section class="caption">
  <h1><PERSON><PERSON><PERSON><PERSON> szerz<PERSON>im</h1>
</section>

<section class="content">
  <ng-container *ngFor="let author of authors$ | async; let i = index">
    <man-author-info [data]="author"></man-author-info>
  </ng-container>
</section>

<section class="show-more">
  <man-simple-button (click)="loadMore()" *ngIf="currentPage < maxPage">
    <span>Továbbiak betöltése</span>
  </man-simple-button>
</section>
