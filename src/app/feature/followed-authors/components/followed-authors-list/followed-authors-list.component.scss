@use 'shared' as *;

:host {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  flex-direction: column;

  @include media-breakpoint-down(md) {
    padding: 0 20px;
  }
}

man-breadcrumb {
  margin-left: 2px;
}

.caption {
  h1 {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    color: var(--kui-orange-600);
    margin: 20px 0 10px 0;
    border-bottom: 1px solid var(--kui-gray-100);
    padding-bottom: 10px;
  }
}

.content {
  margin: 0 auto;
  width: 100%;

  @media screen and (min-width: 996px) {
    max-width: 996px;
  }
}

.show-more {
  margin: 30px auto 70px auto;
}
