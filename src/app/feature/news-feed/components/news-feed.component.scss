@use 'shared' as *;

:host {
  ::ng-deep {
    mandiner-social-share-modal {
      .share-text {
        font-size: 12px;
        font-weight: 400;
        color: var(--kui-gray-700);
        margin-right: 5px;
      }
    }

    man-spinner {
      display: block;
    }
  }

  section {
    margin: 40px 0;

    .wrapper {
      flex-direction: row-reverse;

      @include media-breakpoint-down(md) {
        flex-direction: column-reverse;
      }
    }

    .title {
      font-size: 32px;
      font-weight: 700;
      line-height: 42px;
      font-family: var(--kui-font-secondary);
      margin-bottom: 20px;

      &:hover {
        color: var(--kui-orange-600);
      }
    }

    .lead {
      font-family: var(--kui-font-primary);
      font-size: 20px;
      font-weight: 400;
      line-height: 28px;
      margin-bottom: 30px;
    }

    .divider {
      margin: 48px 0;
      background-color: var(--kui-black);
      height: 1px;
    }

    .dossier {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      gap: 24px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
      }

      figure,
      .left-column {
        width: 50%;

        @include media-breakpoint-down(md) {
          width: 100%;
        }
      }

      &-title {
        font-size: 40px;
        font-family: var(--kui-font-secondary);
        line-height: 52px;
        font-weight: 700;
        font-style: normal;
        margin-bottom: 10px;

        &:hover {
          color: var(--kui-orange-600);
        }
      }

      &-lead {
        @extend .lead;
        margin-bottom: 10px;
      }

      &-date {
        font-size: 12px;
        font-weight: 600;
        line-height: 12px;
        color: var(--kui-gray-700);
      }

      &-thumbnail {
        width: 100%;
        aspect-ratio: 16 / 9;
        object-fit: cover;
        max-height: 320px;
      }

      &-caption {
        color: var(--kui-gray-775);
        flex-wrap: wrap;
        font-size: 12px;
        font-style: normal;
        line-height: 18px;
        font-weight: 400;
      }

      &-source {
        @extend .dossier-caption;
        font-weight: 300;
      }

      figcaption {
        display: flex;
        flex-direction: column;
        margin: 10px 40px 0 40px;
      }
    }

    .news-feed {
      position: sticky;
      top: 70px;

      @include media-breakpoint-down(md) {
        margin-top: 20px;
      }

      &-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 26px;
        padding: 20px;
        border-top: 4px solid var(--kui-orange-600);
        background-color: rgba(176, 133, 32, 0.3);
      }

      &-lead {
        background-color: var(--kui-gray-75);
        line-height: 24px;
        padding: 20px;
      }

      &-share {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        ::ng-deep {
          .share-text {
            display: none;
          }
        }
      }
    }

    .meta {
      display: flex;
      justify-content: space-between;
      gap: 20px;

      &-date {
        font-size: 12px;
        font-weight: 600;
        color: var(--kui-gray-700);
      }
    }
  }
}

.wrapper {
  #related-articles.double-recommendations {
    border-top: 1px solid var(--kui-gray-100);
    padding-top: 30px;
  }
}

.dossier-recommender-wrapper {
  display: block;
  margin-bottom: 20px;
}
