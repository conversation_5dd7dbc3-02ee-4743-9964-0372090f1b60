import { Injectable } from '@angular/core';
import { Params, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, BackendArticleSearchResult, LayoutService, LayoutWithExcludeIds } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { ApiService } from '../../shared';
import { articlesPageSize, CategoryServiceResponse } from './category.definitions';
import { mapCategoryResponse } from './category.utils';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  excludedArticleIds: string[] = [];

  public constructor(
    private readonly apiService: ApiService,
    private readonly layoutService: LayoutService,
    private readonly router: Router
  ) {}

  public getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryServiceResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      tap((data: LayoutWithExcludeIds) => {
        if (!data.columnTitle) {
          this.router
            .navigate(['/', '404'], {
              state: {
                errorResponse: JSON.stringify(data),
              },
              skipLocationChange: true,
            })
            .then();
        }
      }),
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        this.excludedArticleIds = layoutResponse.excludedIds;
        return this.apiService.searchByKeyword(undefined, 'desc', undefined, undefined, undefined, [categorySlug], 0, articlesPageSize).pipe(
          map((res) => {
            if (pageIndex > 0 && res.data.length === 0) {
              throw new Error('No more items');
            }
            return res;
          }),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: {
                  errorResponse: JSON.stringify(error),
                },
                skipLocationChange: true,
              })
              .then();
            return throwError(error);
          }),
          map((categoryResponse: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => ({
            categoryResponse: mapCategoryResponse(categoryResponse, categorySlug, '', '', layoutResponse),
            categoryLayoutResponse: layoutResponse,
          }))
        );
      }),
      map(({ categoryResponse, categoryLayoutResponse }) => {
        if (categoryResponse.columnParentTitle === undefined || categoryResponse.columnParentTitle === typeof undefined) {
          categoryResponse = {
            ...categoryResponse,
            columnParentTitle: categoryLayoutResponse.columnParentTitle,
          };
        }
        return categoryResponse;
      })
    );
  }

  public getRequestForCategoryByDate(params: Params, queryParams: Params): Observable<CategoryServiceResponse> {
    const { categorySlug, year, month } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    if (isNaN(year) || isNaN(month)) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }

    const request$ = this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse) =>
        this.apiService.getCategoryArticles(categorySlug, pageIndex, articlesPageSize, year, month, []).pipe(
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) =>
            mapCategoryResponse(categoryResponse, categorySlug, year, month, layoutResponse as LayoutWithExcludeIds, true)
          ),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: {
                  errorResponse: JSON.stringify(error),
                },
                skipLocationChange: true,
              })
              .then();
            return throwError(error);
          })
        )
      )
    );

    return request$;
  }
}
