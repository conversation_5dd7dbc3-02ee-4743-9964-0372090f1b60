<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else adultContent" class="article-page">
  <man-adult (isUserAdult)="onIsUserAdultChoose($event)"></man-adult>
</ng-container>

<ng-template #adultContent>
  <section *ngIf="article" class="article-page">
    <div class="wrapper with-aside">
      <man-breadcrumb *ngIf="breadcrumbItems" [items]="breadcrumbItems"></man-breadcrumb>
    </div>
    <!-- Trending topics by tags -->
    <div class="wrapper with-aside">
      <man-trending-topics
        *ngIf="article.tags && article.tags.length > 0"
        [data]="article.tags"
        [foundationTagSlug]="foundationTagSlug"
        [foundationTagTitle]="foundationTagTitle"
        class="w-100"
      ></man-trending-topics>
      <div [ngClass]="{ 'left-column': !foundationTagSlug }">
        <h1 class="article-page-title">{{ article?.title }}</h1>
        <ng-container *ngTemplateOutlet="publishDate; context: { isMobile: true }"></ng-container>
        <p class="article-page-lead">{{ article?.lead || article?.excerpt }}</p>

        <div class="article-page-social">
          <ng-container *ngTemplateOutlet="publishDate"></ng-container>
          <ng-container *ngTemplateOutlet="socialInteractions; context: { isPositionRight: true }"></ng-container>
        </div>
        <figure *ngIf="article.thumbnail && (article.hideThumbnailFromBody !== undefined ? !article.hideThumbnailFromBody : true)">
          <img
            [alt]="article.thumbnailInfo?.altText"
            [data]="article?.thumbnailFocusedImages"
            [displayedAspectRatio]="{ desktop: '16:9' }"
            [displayedUrl]="article.thumbnail"
            class="article-page-thumbnail"
            loading="eager"
            withFocusPoint
          />
        </figure>

        <app-strossle-advert advertId="Mandiner_normal_content_1"></app-strossle-advert>

        <man-author-info
          (followEvent)="handleAuthorFollow(authorData?.slug ?? '')"
          *ngIf="authorData?.name"
          [data]="authorData"
          [isFollowLoading]="isAuthorFollowButtonLoading"
          [isFollowed]="isAuthorFollowed$ | async"
          [isInnerAuthor]="isInnerAuthor"
          [isOpinion]="article?.isOpinion"
          [showAuthorDetails]="false"
          [showAuthorRank]="false"
          [showButton]="article.materialType === 'own_material'"
        ></man-author-info>

        <!-- body -->
        <ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>
        @if (olimpiaService.isEnableOlympicsElements()) {
          <kesma-olimpia-navigator [styleID]="OlimpicPortalEnum.OlimpicMANDINER" [navigationLink]="['/', 'olimpia-2024']"></kesma-olimpia-navigator>
        }
        <man-subscription-block
          *ngIf="article?.isPaywalled && !(user$ | async)?.hasValidSubscription"
          [isLoggedIn]="isLoggedIn"
          buttonText="Tovább az előfizetésekhez"
        >
        </man-subscription-block>

        <app-calendar [shouldClose]="false" [type]="CalendarType.Article"></app-calendar>

        <!-- Minute by minute -->
        <div *ngIf="article?.minuteToMinute !== MinuteToMinuteState.NOT" class="minute-by-minute">
          <div class="minute-by-minute-running">
            <h3 *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING" class="minute-by-minute-info">
              Élő közvetítésünket lejjebb görgetve olvashatják.
            </h3>
            <a (click)="refreshArticle()" class="minute-by-minute-refresh">
              Frissítés
              <img alt="Frissítés" src="assets/images/icons/icon-refresh.svg" />
            </a>
          </div>
          <div *ngFor="let block of article.minuteToMinuteBlocks" class="minute-by-minute-block">
            <div class="minute-by-minute-block-header">
              <div *ngIf="block.date" class="minute-by-minute-block-time">{{ block.date | formatDate: 'h-m' }}</div>
              <h5 class="minute-by-minute-block-title">{{ block.title }}</h5>
            </div>
            <div class="minute-by-minute-block-content">
              <ng-container [ngTemplateOutletContext]="{ body: block.body }" [ngTemplateOutlet]="bodyContent"></ng-container>
            </div>
          </div>
        </div>
        <div *ngIf="article.isOpinion && article?.articleSource" class="article-source">
          <a [href]="article.articleSource" class="article-source-text" target="_blank">
            az eredeti, teljes írást itt olvashatja el
            <img alt="Navigálás" class="article-source-text-img" src="/src/assets/images/icons/arrow-right-black.svg" />
          </a>
        </div>
        <div #dataTrigger *ngIf="article"></div>
        <div id="Mandiner_egyedi_normal_content_cikk"></div>

        <ng-container *ngIf="!foundationTagSlug && relatedArticles?.length !== 0">
          <div id="related-articles">
            <ng-container *ngFor="let article of relatedArticles; let i = index">
              <man-article-card
                (socialInteraction)="onSocialInteraction($event)"
                [data]="article"
                [hideDate]="true"
                [isCategoryVisible]="true"
                [isMaxWidth]="true"
                [isMplus]="article?.isPaywalled"
                [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
              >
              </man-article-card>
              <ng-container *ngIf="i === 2">
                <app-strossle-advert class="mobile" advertId="Mandiner_rectangle_mobil"></app-strossle-advert>
                <app-strossle-advert advertId="Mandiner_normal_content_7"></app-strossle-advert
              ></ng-container>
              <app-strossle-advert *ngIf="i === 5" advertId="Mandiner_normal_content_8"></app-strossle-advert>
            </ng-container>
          </div>
        </ng-container>

        <app-foundation-recommendation
          *ngIf="foundationTagSlug && articleSlug"
          [articleSlug]="articleSlug"
          [foundationTagSlug]="foundationTagSlug"
          [tags]="article.tags"
        ></app-foundation-recommendation>

        <app-article-dossier-recommender
          *ngIf="dossier"
          [excludedArticleSlug]="articleSlug"
          [subsequentDossier]="dossier"
          class="dossier-recommender-wrapper"
        ></app-article-dossier-recommender>

        <man-newsletter-diverter-card (subscribeClicked)="onSubscribeClicked()" [styleID]="NewsletterDiverterCardType.Article"></man-newsletter-diverter-card>
        <app-strossle-advert advertId="Mandiner_normal_content_9"></app-strossle-advert>
        <app-comment-section
          #commentSection
          (commentSubmitted)="handleCommentSubmitted()"
          *ngIf="article && !article.isCommentsDisabled"
          [allCommentCount]="socialInteractionData?.comment ?? 0"
          [articleID]="article.id"
          id="kommentek"
        >
        </app-comment-section>

        <app-strossle-advert advertId="Mandiner_normal_content_10"></app-strossle-advert>

        <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>

        <app-external-recommendations></app-external-recommendations>

        <app-strossle-advert advertId="Mandiner_normal_content_12"></app-strossle-advert>

        <ng-container *ngIf="electionsService.isElections2024Enabled()">
          <kesma-elections-box
            [desktopWidth]="foundationTagSlug ? 12 : 9"
            [link]="electionsService.getElections2024Link()"
            [styleID]="ElectionsBoxStyle.DIVERTER"
          ></kesma-elections-box>
        </ng-container>
      </div>
      <aside *ngIf="!foundationTagSlug">
        <app-sidebar *ngIf="articleSlug && categorySlug" [articleId]="article?.id" [articleSlug]="articleSlug" [categorySlug]="categorySlug"></app-sidebar>
      </aside>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <man-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></man-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.Voting" class="voting-block">
        @if (voteCache[element.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <man-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [styleID]="votingStyle.GRAY" [voteId]="voteData.votedId" />
        }
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
        <app-article-dossier-recommender
          *ngIf="element?.details[0]?.value?.isActive && element?.details[0]?.value?.relatedArticles?.length"
          [excludedArticleSlug]="articleSlug"
          [subsequentDossier]="element?.details[0]?.value"
          class="dossier-recommender-wrapper"
        ></app-article-dossier-recommender>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <mandiner-man-quiz [data]="element?.details[0]?.value"></mandiner-man-quiz>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-video">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <app-strossle-advert [advertId]="element.advert"></app-strossle-advert>
      </ng-container>

      <div *ngSwitchCase="ArticleBodyType.Gallery" class="block-gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id] as gallery">
          <man-gallery-card
            [data]="gallery"
            [isInsideAdultArticleBody]="article?.isAdultsOnly ?? false"
            [routerLink]="['/galeria', gallery?.slug]"
          ></man-gallery-card>
        </ng-container>
      </div>

      <div *ngSwitchCase="ArticleBodyType.Article" class="recommendation-block">
        <ng-container *ngIf="element?.details[0]?.value?.isOpinion; else articleCard">
          <div class="opinion-card-outer-wrapper">
            <div class="opinion-card-wrapper">
              <p class="opinion-card-title">Kapcsolódó vélemény</p>
              <man-opinion-card
                [data]="element?.details[0]?.value"
                [fontSize]="24"
                [styleID]="ManOpinionCardType.ARTICLE_DETAIL_STYLE"
                class="opinion-card"
              ></man-opinion-card>
            </div>
          </div>
        </ng-container>

        <ng-template #articleCard>
          <div class="recommendation-card-outer-wrapper">
            <div class="recommendation-card-wrapper">
              <p class="recommendation-card-title">Ezt is ajánljuk a témában</p>
              <man-article-card
                (socialInteraction)="onSocialInteraction($event)"
                [data]="element?.details[0]?.value"
                [fontSize]="20"
                [styleID]="ArticleCardType.ArticleDetailStyle"
                class="recommendation-card"
              ></man-article-card>
            </div>
          </div>
        </ng-template>
      </div>

      <div *ngSwitchCase="'DoubleArticleRecommendationOptional.Double'" class="wrapper">
        <man-block-title-small [data]="{ text: 'Kapcsolódó cikkek' }" [isFullWidth]="true"></man-block-title-small>
        <div class="double-recommendations" id="double-recommendations">
          <man-article-card
            (socialInteraction)="onSocialInteraction($event)"
            *ngFor="let article of doubleArticleRecommendations(element.details); let i = index"
            [data]="doubleArticleRecommendation(article.value)"
            [hideDate]="true"
            [isCategoryVisible]="true"
            [isMaxWidth]="true"
            [styleID]="ArticleCardType.ImgRightTitleLeadDateMeta"
          >
          </man-article-card>
        </div>
      </div>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #publishDate let-isMobile="isMobile">
  <div [class.mobile]="isMobile" class="article-page-published">
    {{ article?.publishDate | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
  </div>
</ng-template>

<ng-template #socialInteractions let-isPositionRight="isPositionRight">
  <mandiner-social-interactions
    (bookmarkClicked)="handleBookmartClicked($event)"
    (commentClicked)="scrollToComments()"
    (reactionClicked)="handleReactionClicked($event)"
    (socialInteraction)="onSocialInteraction($event)"
    [articleLink]="articleLink"
    [articleTitle]="article.title"
    [data]="socialInteractionData"
    [isBookmarked]="isBookmarked"
    [isPositionRight]="isPositionRight"
    [isReactionEnabled]="true"
    [isShowBookmark]="true"
    [isShowReadingTime]="true"
    [isShowText]="true"
    class="article-page-social-interactions"
  ></mandiner-social-interactions>
</ng-template>
