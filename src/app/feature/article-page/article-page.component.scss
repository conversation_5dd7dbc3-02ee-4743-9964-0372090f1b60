@use 'shared' as *;

.article-page {
  margin-bottom: 60px;

  .wrapper.with-aside {
    margin-top: 20px;
  }

  man-breadcrumb {
    @include media-breakpoint-down(md) {
      margin-left: 0;
    }
  }

  man-trending-topics::ng-deep {
    .trending-topics-topic {
      margin-top: 0px !important;
      line-height: 14px;
      padding: 8px 10px 7px 10px;
    }
  }

  &-figcaption {
    color: var(--kui-gray-775);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    margin-bottom: 30px;
    margin-top: 10px;
    text-align: center;
  }

  &-published {
    font-size: 12px;
    line-height: 12px;
    font-weight: 600;
    min-width: 175px;
    margin-top: 5px;

    &.mobile {
      display: none;
    }

    @include media-breakpoint-down(sm) {
      display: none;

      &.mobile {
        display: block;
        margin: 10px 0;
      }
    }
  }

  &-social {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    gap: 10px;

    &-interactions {
      border: none;
    }
  }

  &-title {
    font-family: var(--kui-font-secondary);
    font-size: 28px;
    font-weight: 700;
    line-height: 34px;
    margin-bottom: 10px;
  }

  &-lead {
    font-family: var(--kui-font-primary);
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  &-thumbnail {
    width: 100%;
    object-fit: cover;
    margin-bottom: 30px;
  }

  man-subscription-block {
    margin: 15px 0;
  }
}

:host {
  ::ng-deep {
    #related-articles,
    #double-recommendations {
      .article-card-figure-image {
        max-width: 160px;
        min-width: 160px;
        height: 90px;

        @include media-breakpoint-down(md) {
          height: 90px;
          aspect-ratio: 1/1;
          max-width: 90px;
          min-width: 90px;
        }
      }
    }

    man-wysiwyg-box {
      .block-content {
        .custom-text-style {
          &.underlined-text {
            margin: 0;
            font-weight: normal;
          }
        }
      }
    }
  }
}

man-author-info {
  margin-bottom: 30px;
  margin-top: -20px;
  max-width: 100%;
}

app-article-dossier-recommender::ng-deep {
  .block-title {
    display: none;
  }
}

.recommend-title-row {
  margin-bottom: 30px;
}

hr:nth-of-type(2) {
  margin-bottom: 30px;
}

.more-articles-by-author {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;

  &-title {
    font-weight: bold;
    text-decoration: underline;
    margin-bottom: 20px;
  }

  .opinion-card {
    margin-bottom: 15px;
    flex-basis: calc(33.33% - 10px);
    display: block;

    @include media-breakpoint-down(md) {
      flex-basis: calc(50% - 10px);
    }
  }

  @include media-breakpoint-down(md) {
    display: block;
  }
}

.voting-block {
  margin-bottom: 30px;
}

.dossier-recommender-wrapper {
  display: block;
  margin-bottom: 20px;
}

.recommendation-block {
  display: flex;
  flex-direction: column;
  grid-gap: 34px 16px;
  margin-bottom: 4px;
  margin-top: 36px;

  .recommendation-card {
    margin-bottom: 0;

    &-title {
      font-size: 12px;
      font-weight: 700;
      line-height: 18px;
      text-transform: uppercase;
      display: inline-block;
      margin-bottom: 10px;
      color: var(--kui-orange-600);
    }

    &-wrapper {
      background-color: var(--kui-gray-60);
      border-left: 1px solid var(--kui-orange-600);
      padding: 10px 20px 20px;
    }

    &-outer-wrapper {
      margin-bottom: 20px;
    }
  }

  .opinion-card {
    &-title {
      font-size: 12px;
      font-weight: 400;
      line-height: 16.2px;
      text-transform: uppercase;
      display: inline-block;
      border-bottom: 3px solid var(--kui-orange-600);
      margin-bottom: 10px;
    }

    &-wrapper {
      background-color: var(--kui-gray-75);
      padding: 10px 20px;
      border: 1px solid var(--kui-gray-300);
    }

    &-outer-wrapper {
      border-top: 1px solid var(--kui-gray-50);
      border-bottom: 1px solid var(--kui-gray-50);
      padding: 24px 0;
      margin-bottom: 20px;
    }
  }

  man-article-card.style-ImgRightTitleLeadDateMeta {
    padding-bottom: 0;
  }

  @include media-breakpoint-down(md) {
    > metropol-article-card {
      margin-bottom: 15px;
    }
  }

  .full-row {
    grid-column: span 3 / span 6;

    @include media-breakpoint-down(md) {
      grid-column: span 1;
    }
  }
}

.article-source {
  height: 56px;

  @include media-breakpoint-down(sm) {
    display: flex;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    line-height: 24px;
    text-transform: uppercase;
    color: #d74929;

    @include media-breakpoint-down(sm) {
      font-size: 14px;
    }

    &-img {
      margin-bottom: 3px;
    }
  }
}

.article-bottom-social-interaction {
  height: 56px;
  display: flex;
  align-items: center;
  border-top: solid 1px var(--kui-gray-100);
}

.trending-topics {
  border-top: solid 1px var(--kui-gray-100);
  border-bottom: solid 1px var(--kui-gray-100);
}

.minute-by-minute {
  &-running {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 48px;
    gap: 10px;
  }

  &-info {
    font-style: italic;
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
  }

  &-refresh {
    cursor: pointer;
    color: var(--kui-orange-600);
    font-weight: 400;
    font-size: 18px;
    line-height: 18px;
    text-transform: uppercase;
    display: flex;
    gap: 10px;
    flex: 0 0 auto;
  }

  &-block {
    &-header {
      display: flex;
      font-weight: 700;
      font-size: 18px;
      line-height: 28px;
      border-top: 1px solid var(--kui-gray-100);
      padding: 10px 0;
    }

    &-time {
      color: var(--kui-orange-600);
    }

    &-title {
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-size: 18px;
      line-height: 28px;
      margin-left: 10px;
    }
  }
}

#related-articles {
  padding-top: 10px;
  border-top: 1px solid var(--kui-gray-50);
}

.wrapper {
  #related-articles.double-recommendations {
    border-top: 1px solid var(--kui-gray-100);
    padding-top: 30px;
    margin-bottom: 24px;
  }
}

.w-100 {
  width: 100%;
}

kesma-elections-box {
  margin: 30px 0;
}

.mobile {
  display: block;

  @include media-breakpoint-up(sm) {
    display: none;
  }
}
