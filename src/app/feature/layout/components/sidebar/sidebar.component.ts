import { NgIf } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { EmbeddingService, UtilService } from '@trendency/kesma-core';
import { LayoutArticle, Layout, LayoutContent, LayoutElementRow, LayoutPageType, LayoutService } from '@trendency/kesma-ui';
import { forkJoin, of } from 'rxjs';
import { catchError, mergeMap, tap } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { environment } from 'src/environments/environment';
import { LayoutComponent } from '../layout/layout.component';
import { MandinerWidgetComponent } from '../../../../shared/components/mandiner-widget/mandiner-widget.component';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LayoutComponent, MandinerWidgetComponent],
})
export class SidebarComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() categorySlug?: string;
  @Input() articleId?: string;
  @Input() articleSlug?: string;
  @Input() columnSlug?: string;
  @Input() excludedIds: string[] = [];
  @Output() populatedEvent = new EventEmitter<boolean>();
  layoutApiData?: Layout;
  layoutReady = false;

  LayoutPageType = LayoutPageType;

  constructor(
    private readonly api: ApiService,
    private readonly layoutService: LayoutService,
    private readonly embedding: EmbeddingService,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.populateSidebar();
  }

  get structure(): LayoutElementRow[] {
    return (this.layoutApiData?.struct || []) as LayoutElementRow[];
  }

  populateSidebar(): void {
    const allExcludedId = [...(this.articleId ? [this.articleId] : []), ...this.excludedIds];

    this.layoutService
      .getSidebar(this.categorySlug, this.articleSlug)
      .pipe(
        tap(() => {
          this.layoutApiData = undefined;
          this.changeRef.detectChanges();
        }),
        catchError(() =>
          of({
            data: {
              content: [],
              struct: [],
            },
          })
        )
      )
      .pipe(
        mergeMap((layoutApiResponse) =>
          forkJoin([
            of(layoutApiResponse),
            this.api.getSidebarArticleRecommendations(this.getArticleCount(layoutApiResponse?.data?.content), this.categorySlug, allExcludedId).pipe(
              catchError((error, _) => {
                if (!environment.production && this.utilsService.isBrowser()) {
                  console.warn('[dev] Unable to download sidebar: ', error);
                }
                return [];
              })
            ),
          ])
        )
      )
      .subscribe(([{ data: layoutApiData }, { data: recommendedArticles }]) => {
        this.layoutApiData = layoutApiData as Layout;
        this.fillLayoutContent(recommendedArticles as LayoutArticle[], []);
        this.layoutReady = true;
        this.changeRef.detectChanges();
        setTimeout(() => {
          this.embedding.loadEmbedMedia();
        }, 0);
        this.populatedEvent.emit(true);
        this.changeRef.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.embedding.loadEmbedMedia();
    }, 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // need sidebar refetch to avoid showing the same article in sidebar that is currently displayed on detail page
    if (
      (changes['articleId']?.currentValue && !changes['articleId']?.firstChange) ||
      (changes['excludedIds']?.currentValue && !changes['excludedIds']?.firstChange)
    ) {
      this.populateSidebar();
    }
  }

  getArticleCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedArticles }) => selectedArticles).reduce((acc, val) => acc.concat(val), []).length;
  }

  getOpinionCount(content: LayoutContent[]): number {
    // no flat() ??? -> upgrade to es2019
    return content.map(({ selectedOpinions }) => selectedOpinions).reduce((acc, val) => acc.concat(val), []).length;
  }

  fillLayoutContent(articles: LayoutArticle[], opinions: LayoutArticle[]): void {
    let articleCursor = 0;
    let opinionCursor = 0;
    this.layoutApiData?.content.forEach(({ selectedArticles, selectedOpinions }) => {
      if (selectedArticles?.length) {
        for (let i = 0; i < selectedArticles.length; i++) {
          // only overwrite null values
          if (!selectedArticles[i] && articles[articleCursor]) {
            selectedArticles[i] = {
              id: articles[articleCursor]?.id ?? '',
              data: articles[articleCursor],
            };
            articleCursor++;
          }
        }
      } else if (selectedOpinions?.length) {
        for (let i = 0; i < selectedOpinions.length; i++) {
          // only overwrite null values
          if (!selectedOpinions[i]) {
            selectedOpinions[i] = {
              id: opinions[opinionCursor]?.id ?? '',
              data: opinions[opinionCursor],
            };
            opinionCursor++;
          }
        }
      }
    });
  }
}
