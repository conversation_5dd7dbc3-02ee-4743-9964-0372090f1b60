<ng-container *ngIf="data$ | async as data">
  <man-article-card
    [styleID]="ARTICLE_CARD_TYPE"
    [data]="toArticleCard(data)"
    [isMplus]="toArticleCard(data)?.isPaywalled"
    [isMaxWidth]="true"
    [isTagVisible]="true"
  ></man-article-card>
  <div class="comment" *ngFor="let comment of comments$ | async">
    <h4>{{ username }}</h4>
    <span>{{ comment.createdAt | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}</span>
    <p>{{ comment.commentText }}</p>
  </div>
  <man-simple-button *ngIf="hasMoreComments$ | async" color="link" (click)="loadMore()" class="mb-2">
    <span class="to-comments">További{{ isOwnComments ? ' saját' : '' }} kommentek betöltése <i class="icon icon-mandiner-chevron-right rotate-90"></i></span>
  </man-simple-button>
  <a class="to-comments" [routerLink]="getUrl()" fragment="kommentek">
    Tovább a kommentekhez
    <i class="icon icon-mandiner-chevron-right"></i>
  </a>
</ng-container>
