import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { SearchFilterDataService, SearchFilterDefinitions, ApiService, SecureApiService, AuthService } from '../../../../shared';
import { catchError, map } from 'rxjs/operators';
import { User } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class UserCommentsResolver {
  constructor(
    private readonly searchFilterDataService: SearchFilterDataService,
    private readonly apiService: ApiService,
    private readonly secureApiService: SecureApiService,
    private readonly authService: AuthService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<{
    publicUser: User | null;
    commentCount: number;
    filterColumns: SearchFilterDefinitions;
  }> {
    const id = route.params['id'];

    return forkJoin([
      id ? this.apiService.getPublicUser(route.params['id']) : of(null),
      id ? this.secureApiService.getCommentsCount(route.params['id']) : of(null),
      this.searchFilterDataService.getSearchFilterColoumns(),
    ]).pipe(
      map(([publicUser, commentCount, filterColumns]) => ({
        publicUser: publicUser ? this.authService.mapBackendUserResponseToUser(publicUser) : null,
        commentCount: commentCount?.data?.commentCount ?? 0,
        filterColumns,
      })),
      catchError(() => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(null);
      })
    );
  }
}
