<main class="container" *ngIf="data$ | async as data">
  <man-breadcrumb [items]="[{ label: 'Hetilap' }]"></man-breadcrumb>

  <app-journal-recommender [data]="data?.[0]" (subscribeClicked)="subscribe()" (pdfClicked)="pdfClicked(data?.[0])"></app-journal-recommender>

  <section class="previous-journals">
    <div class="previous-journals-header not-mobile">
      <h3 class="font-section-subtitle">Mandiner hetilap</h3>
      <h2 class="font-section-title">Előző hetilapok</h2>
    </div>
    <div class="previous-journals-grid">
      <article *ngFor="let item of data | slice: 1">
        <ng-container *ngTemplateOutlet="previousJournal; context: { $implicit: item }"></ng-container>
      </article>
      <article *ngFor="let item of moreArticles$ | async">
        <ng-container *ngTemplateOutlet="previousJournal; context: { $implicit: item }"></ng-container>
      </article>
    </div>
  </section>

  <div class="spinner" *ngIf="loading$ | async">
    <div class="spinner-content"></div>
  </div>

  <div class="buttons">
    <man-simple-button [wide]="true" (click)="loadMore()" color="tertiary" [disabled]="(hasMore$ | async) === false">Mutass többet</man-simple-button>
    <man-simple-button *ngIf="!(user$ | async)?.hasValidSubscription" [wide]="true" (click)="subscribe()" color="primary">
      Előfizetek a hetilapra
    </man-simple-button>
  </div>
</main>

<ng-template #previousJournal let-item>
  <a [routerLink]="[item.urlSlug]">
    <img [src]="item?.imageThumbnailUrl" [alt]="'Hetilap: ' + item?.name" />
    <h2>{{ item?.name }}</h2>
  </a>

  <p>Megjelent: {{ item?.publishDate | manTranslatedDatePipe }}</p>
  <man-simple-button *ngIf="item?.pdfEmbedCode" color="light" [wide]="true" (click)="pdfClicked(item)">Lapozható PDF</man-simple-button>
</ng-template>
