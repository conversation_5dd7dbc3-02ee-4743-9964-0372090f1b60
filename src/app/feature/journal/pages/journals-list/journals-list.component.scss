@use 'shared' as *;
@use '../../style/journal-typography' as *;

.ad-container {
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  padding: 20px 0 30px 0;
}

.container {
  width: fit-content;
  max-width: 1200px;
  margin: 20px auto 10px auto;

  @include media-breakpoint-down(md) {
    max-width: 100%;
    margin: 20px;
  }
}

man-breadcrumb {
  margin-bottom: 40px;
  @include media-breakpoint-down(md) {
    margin: 0 0 20px 20px;
  }
}

.previous-journals {
  &-header {
    margin: 50px 0;
    padding: 20px 0 20px 0;
    border-bottom: 1px solid var(--kui-gray-250);
    border-top: 1px solid var(--kui-gray-250);

    h3 {
      margin-bottom: 10px;
    }

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px 0;
    max-width: calc(100vw - 40px);

    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(1, 1fr);
    }

    article {
      img {
        width: 100%;
        aspect-ratio: 37/50;
      }

      h2 {
        font-weight: 700;
        font-size: 24px;
        font-family: var(--kui-font-primary);
        color: var(--kui-orange-600);
        padding-bottom: 10px;
        border-bottom: 1px solid var(--kui-gray-250);
      }

      p {
        margin: 10px 0;
        font-weight: 400;
        font-size: 14px;
      }
    }
  }
}

.spinner {
  margin: 20px auto;
  width: fit-content;

  &-content {
    border: 16px solid var(--kui-gray-50); /* Light grey */
    border-top: 16px solid var(--kui-orange-600); /* Blue */
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.buttons {
  display: flex;
  justify-content: center;
  width: 588px;
  gap: 24px;
  margin: 0 auto 40px auto;

  man-simple-button {
    max-width: 280px;
  }

  @media only screen and (max-width: 628px) {
    flex-direction: column;
    width: 100%;

    man-simple-button {
      max-width: unset;
    }
  }
}

.not-mobile {
  @media only screen and (max-width: 672px) {
    display: none;
    visibility: hidden;
  }
}
