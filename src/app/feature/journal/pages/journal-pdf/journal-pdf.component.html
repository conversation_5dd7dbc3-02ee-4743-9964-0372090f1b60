<div class="pdf-viewer" *ngIf="pdfEmbedCode$ | async as pdfEmbedCode">
  <object *ngIf="isSafari; else iframe" [data]="pdfEmbedCode | bypass: 'resourceUrl'" type="application/pdf" class="pdf-iframe" width="100%">
    <p>A böngészője nem alkalmas a PDF megjelenítésére. Kérjük váltson másik böngészőre.</p>
  </object>

  <ng-template #iframe>
    <iframe
      class="pdf-iframe"
      title="Lapozható Hetilap"
      [src]="pdfEmbedCode | bypass: 'resourceUrl'"
      sandbox="allow-top-navigation allow-top-navigation-by-user-activation allow-downloads allow-scripts allow-same-origin allow-popups allow-modals allow-popups-to-escape-sandbox allow-forms"
      allowfullscreen="allowfullscreen"
    >
    </iframe>
  </ng-template>
</div>
