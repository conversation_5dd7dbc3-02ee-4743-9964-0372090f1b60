@use 'shared' as *;
@use '../../style/journal-typography' as *;

:host {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
  gap: 24px;
  grid-template-areas: 'cover details';
  width: 100%;
  max-width: calc(100vw - 40px);

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-template-areas: 'cover details';
  }

  @include media-breakpoint-down(sm) {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  &.showing-articles {
    grid-template-columns: 1fr 1fr 0.73fr;
    grid-template-areas: 'cover details highlighted';

    @include media-breakpoint-down(md) {
      grid-template-rows: repeat(2, auto);
      grid-template-columns: 1fr 1fr;
      grid-template-areas: 'cover details' 'highlighted highlighted';
    }

    @include media-breakpoint-down(sm) {
      grid-template-rows: repeat(5, auto);
      grid-template-areas: 'title' 'cover' 'actions' 'details' 'highlighted';
    }
  }
}

.cover {
  grid-area: cover;
  max-width: 100%;
  &-image {
    width: 100%;
    aspect-ratio: 37/50;
  }
}

.actions {
  grid-area: actions;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;

  @include media-breakpoint-down(sm) {
    margin-top: -10px;
    gap: 10px;
  }
}

.details {
  grid-area: details;
  width: 100%;

  h1 {
    @extend %h1;
  }

  .articles-list {
    margin-bottom: 24px;
  }
}

.highlighted {
  grid-area: highlighted;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;

  @include media-breakpoint-down(lg) {
    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: 1fr 1fr;
    }
  }

  article {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--kui-gray-100);
  }

  img {
    width: 100%;
    max-height: 320px;
    aspect-ratio: 16/9;
    object-fit: cover;
  }
}

.title {
  grid-area: title;

  h1 {
    @extend %h1;
  }

  h2 {
    margin: 10px 0 8px 0;
  }

  h3 {
    margin-bottom: 20px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 10px;
    }
  }
}

.only-mobile {
  align-self: flex-start;

  @include media-breakpoint-up(md) {
    display: none;
    visibility: hidden;
  }
}

.not-mobile {
  @include media-breakpoint-down(sm) {
    display: none;
    visibility: hidden;
  }
}

.small-articles {
  list-style: none;
  margin-left: 1.5em;

  li {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
    max-width: 384px;

    &:before {
      content: ' ';
      height: 1em;
      width: 1em;
      display: block;
      float: left;
      margin: 10px 0 0 -1.5em;
      background-repeat: no-repeat;
      background-image: url('/src/assets/images/icons/man-arrow-right.svg');
      background-position: center;
    }
  }

  &-container {
    margin-top: -23px;
    max-width: 384px;
  }
}

.font-publish-date {
  margin: 10px 0 0 0;
}

.mt-24px {
  // Extra space when there are no articles
  margin-top: 24px;
}

%h1 {
  padding-bottom: 10px;
  border-bottom: 1px solid var(--kui-gray-50);
  margin-bottom: 20px;
}

.footnote {
  text-align: center;
  font-family: var(--kui-font-primary);
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px; /* 140% */
  margin-top: 5px;
}
