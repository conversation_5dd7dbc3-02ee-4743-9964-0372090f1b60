(async function () {
  let e = 'html-load.com';
  const t = window,
    r = (e) => new Promise((t) => setTimeout(t, e)),
    o = t.atob,
    a = t.btoa,
    s = (r) => {
      const s = o('VGhlcmUgd2FzIGEgcHJvYmxlbSBsb2FkaW5nIHRoZSBwYWdlLiBQbGVhc2UgY2xpY2sgT0sgdG8gbGVhcm4gbW9yZS4=');
      if (confirm(s)) {
        const o = new t.URL('https://report.error-report.com/modal'),
          s = o.searchParams;
        s.set('url', a(location.href)),
          s.set('error', a(r.toString())),
          s.set('domain', e),
          (location.href = o.href);
      } else location.reload();
    };
  try {
    const c = () =>
      new Promise((e) => {
        let r = Math.random().toString(),
          o = Math.random().toString();
        t.addEventListener('message', (e) => e.data === r && t.postMessage(o, '*')),
          t.addEventListener('message', (t) => t.data === o && e()),
          t.postMessage(r, '*');
      });
    async function n() {
      try {
        let e = !1,
          o = Math.random().toString();
        if (
          (t.addEventListener('message', (t) => {
            t.data === o + '_as_res' && (e = !0);
          }),
          t.postMessage(o + '_as_req', '*'),
          await c(),
          await r(500),
          e)
        )
          return !0;
      } catch (e) {
        console.error(e);
      }
      return !1;
    }
    const i = [100, 500, 1e3];
    for (let l = 0; l <= i.length && !(await n()); l++) {
      if (l === i.length - 1)
        throw (
          o('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA') +
          e +
          o('IGlzIHRhaW50ZWQuIFBsZWFzZSBhbGxvdyA') +
          e
        );
      await r(i[l]);
    }
  } catch (d) {
    console.error(d);
    try {
      t.localStorage.setItem(t.location.host + '_fa_' + a('last_bfa_at'), Date.now().toString());
    } catch (m) {}
    try {
      setInterval(() => document.querySelectorAll('link,style').forEach((e) => e.remove()), 100);
      const h = await (
          await fetch(
            'https://error-report.com/report?type=loader_light&url=' +
              a(location.href) +
              '&error=' +
              a(d),
            { method: 'POST' }
          )
        ).text(),
        g = !1;
      t.addEventListener('message', (e) => {
        'as_modal_loaded' === e.data && (g = !0);
      });
      let p = document.createElement('iframe');
      const f = new t.URL('https://report.error-report.com/modal'),
        u = f.searchParams;
        u.set('url', a(location.href))
        u.set('eventId', h)
        u.set('error', a(d))
        u.set('domain', e);
        (p.src = f.href)
        p.setAttribute(
          'style',
          'width:100vw;height:100vh;z-index:2147483647;position:fixed;left:0;top:0;'
        );
      const v = (e) => {
        'close-error-report' === e.data && (p.remove(), t.removeEventListener('message', v));
      };
      t.addEventListener('message', v); document.body.appendChild(p);
      const w = () => {
          const e = p.getBoundingClientRect();
          return 'none' !== t.getComputedStyle(p).display && 0 !== e.width && 0 !== e.height;
        },
        S = setInterval(() => {
          if (!document.contains(p)) return clearInterval(S);
          w() || (s(d), clearInterval(S));
        }, 1e3);
      setTimeout(() => {
        g || s(d);
      }, 3e3);
    } catch (y) {
      s(y);
    }
  }
})();

window.addEventListener('error', async function () {
  const e = window,
    t = e.atob,
    r = e.btoa;
  let o = JSON.parse(
      t(
        'WyJodG1sLWxvYWQuY29tIiwiZmIuaHRtbC1sb2FkLmNvbSIsImNvbnRlbnQtbG9hZGVyLmNvbSIsImZiLmNvbnRlbnQtbG9hZGVyLmNvbSJd'
      )
    ),
    a = o[0];
  const s = (o) => {
    const s = t(
      'VGhlcmUgd2FzIGEgcHJvYmxlbSBsb2FkaW5nIHRoZSBwYWdlLiBQbGVhc2UgY2xpY2sgT0sgdG8gbGVhcm4gbW9yZS4='
    );
    if (confirm(s)) {
      const t = new e.URL('https://report.error-report.com/modal'),
        s = t.searchParams;
      s.set('url', r(location.href)),
        s.set('error', r(o.toString())),
        s.set('domain', a),
        (location.href = t.href);
    } else location.reload();
  };
  try {
    if (void 0 === e.as_retry && (e.as_retry = 0), e.as_retry >= o.length)
      throw (
        t('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA') +
        a +
        t('IGlzIGJsb2NrZWQuIFBsZWFzZSBhbGxvdyA') +
        a
      );
    const r = document.querySelector('#tQNIlYwsPFK'),
      s = document.createElement('script');
    for (let e = 0; e < r.attributes.length; e++)
      s.setAttribute(r.attributes[e].name, r.attributes[e].value);
    const n = new e.URL(r.getAttribute('src'));
    (n.host = o[e.as_retry++]),
      s.setAttribute('src', n.href),
      r.setAttribute('id', r.getAttribute('id') + '_'),
      r.parentNode.insertBefore(s, r),
      r.remove();
  } catch (t) {
    console.error(t);
    try {
      e.localStorage.setItem(e.location.host + '_fa_' + r('last_bfa_at'), Date.now().toString());
    } catch (e) {}
    try {
      setInterval(() => document.querySelectorAll('link,style').forEach((e) => e.remove()), 100);
      const o = await (
          await fetch(
            'https://error-report.com/report?type=loader_light&url=' +
              r(location.href) +
              '&error=' +
              r(t),
            { method: 'POST' }
          )
        ).text(),
        n = !1;
      e.addEventListener('message', (e) => {
        'as_modal_loaded' === e.data && (n = !0);
      });
      let c = document.createElement('iframe');
      const i = new e.URL('https://report.error-report.com/modal'),
        l = i.searchParams;
      l.set('url', r(location.href)),
        l.set('eventId', o),
        l.set('error', r(t)),
        l.set('domain', a),
        (c.src = i.href),
        c.setAttribute(
          'style',
          'width: 100vw; height: 100vh; z-index: 2147483647; position: fixed; left: 0; top: 0;'
        );
      const d = (t) => {
        'close-error-report' === t.data && (c.remove(), e.removeEventListener('message', d));
      };
      e.addEventListener('message', d), document.body.appendChild(c);
      const m = () => {
          const t = c.getBoundingClientRect();
          return 'none' !== e.getComputedStyle(c).display && 0 !== t.width && 0 !== t.height;
        },
        h = setInterval(() => {
          if (!document.contains(c)) return clearInterval(h);
          m() || (s(t), clearInterval(h));
        }, 1e3);
      setTimeout(() => {
        n || s(t);
      }, 3e3);
    } catch (e) {
      s(e);
    }
  }
});
